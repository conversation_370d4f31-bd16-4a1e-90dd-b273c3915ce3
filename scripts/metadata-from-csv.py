#!/usr/bin/env python3
"""
CSV-Based S3 Metadata Update Tool for YendorCats
Reads metadata from CSV file and applies to S3 objects
"""

import csv
import boto3
import sys
from datetime import datetime
import time

# Configuration
BUCKET_NAME = "yendor"
ENDPOINT_URL = "https://s3.us-west-004.backblazeb2.com"
AWS_ACCESS_KEY_ID = "your_b2_key_id"  # Replace with your key
AWS_SECRET_ACCESS_KEY = "your_b2_application_key"  # Replace with your key

def create_s3_client():
    """Create S3 client for Backblaze B2"""
    return boto3.client(
        's3',
        endpoint_url=ENDPOINT_URL,
        aws_access_key_id=AWS_ACCESS_KEY_ID,
        aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
        region_name='us-west-004'
    )

def update_metadata_from_csv(csv_file_path):
    """
    Update S3 metadata from CSV file
    
    CSV Format:
    s3_key,cat_name,age,date_taken,description,breed,gender,color
    studs/louie/image1.jpg,Louie,3.5,2024-01-15,Beautiful Maine Coon,Maine Coon,M,<PERSON> Tabby
    """
    
    s3_client = create_s3_client()
    
    print("🏷️  Starting CSV-based metadata update...")
    
    try:
        with open(csv_file_path, 'r', newline='', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            
            for row_num, row in enumerate(reader, start=2):  # Start at 2 for header
                try:
                    s3_key = row['s3_key'].strip()
                    cat_name = row['cat_name'].strip()
                    age = row.get('age', '0').strip()
                    date_taken = row.get('date_taken', '').strip()
                    description = row.get('description', '').strip()
                    breed = row.get('breed', 'Maine Coon').strip()
                    gender = row.get('gender', '').strip()
                    color = row.get('color', '').strip()
                    
                    # Validate required fields
                    if not s3_key or not cat_name:
                        print(f"❌ Row {row_num}: Missing required fields (s3_key, cat_name)")
                        continue
                    
                    # Format date if provided
                    if not date_taken:
                        date_taken = datetime.utcnow().isoformat() + 'Z'
                    
                    # Prepare metadata
                    metadata = {
                        'cat-name': cat_name,
                        'age': age,
                        'date-taken': date_taken,
                        'description': description,
                        'breed': breed,
                        'gender': gender,
                        'color': color,
                        'content-type': 'image/jpeg'
                    }
                    
                    # Remove empty values
                    metadata = {k: v for k, v in metadata.items() if v}
                    
                    print(f"📝 Updating: {s3_key} -> {cat_name}")
                    
                    # Copy object to itself with new metadata
                    copy_source = {'Bucket': BUCKET_NAME, 'Key': s3_key}
                    
                    s3_client.copy_object(
                        CopySource=copy_source,
                        Bucket=BUCKET_NAME,
                        Key=s3_key,
                        Metadata=metadata,
                        MetadataDirective='REPLACE',
                        ContentType='image/jpeg'
                    )
                    
                    print(f"✅ Updated: {cat_name}")
                    
                    # Small delay to avoid rate limiting
                    time.sleep(0.5)
                    
                except Exception as e:
                    print(f"❌ Error updating row {row_num}: {e}")
                    continue
                    
    except FileNotFoundError:
        print(f"❌ CSV file not found: {csv_file_path}")
        return False
    except Exception as e:
        print(f"❌ Error reading CSV: {e}")
        return False
    
    print("🎉 CSV metadata update completed!")
    return True

def generate_sample_csv():
    """Generate a sample CSV file for reference"""
    sample_data = [
        {
            's3_key': 'studs/louie/image1.jpg',
            'cat_name': 'Louie',
            'age': '3.5',
            'date_taken': '2024-01-15T10:30:00Z',
            'description': 'Beautiful Maine Coon stud',
            'breed': 'Maine Coon',
            'gender': 'M',
            'color': 'Brown Tabby'
        },
        {
            's3_key': 'queens/bella/image1.jpg',
            'cat_name': 'Bella',
            'age': '2.8',
            'date_taken': '2024-02-20T14:15:00Z',
            'description': 'Elegant Maine Coon queen',
            'breed': 'Maine Coon',
            'gender': 'F',
            'color': 'Silver Tabby'
        }
    ]
    
    with open('sample_metadata.csv', 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['s3_key', 'cat_name', 'age', 'date_taken', 'description', 'breed', 'gender', 'color']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        
        writer.writeheader()
        for row in sample_data:
            writer.writerow(row)
    
    print("📄 Sample CSV generated: sample_metadata.csv")

def list_current_metadata(s3_key):
    """List current metadata for an S3 object"""
    s3_client = create_s3_client()
    
    try:
        response = s3_client.head_object(Bucket=BUCKET_NAME, Key=s3_key)
        metadata = response.get('Metadata', {})
        
        print(f"📋 Current metadata for: {s3_key}")
        print("-" * 50)
        for key, value in metadata.items():
            print(f"{key}: {value}")
        
        if not metadata:
            print("No custom metadata found")
            
    except Exception as e:
        print(f"❌ Error retrieving metadata: {e}")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage:")
        print("  python3 metadata-from-csv.py update <csv_file>")
        print("  python3 metadata-from-csv.py sample")
        print("  python3 metadata-from-csv.py check <s3_key>")
        sys.exit(1)
    
    command = sys.argv[1]
    
    if command == "update" and len(sys.argv) == 3:
        csv_file = sys.argv[2]
        update_metadata_from_csv(csv_file)
    elif command == "sample":
        generate_sample_csv()
    elif command == "check" and len(sys.argv) == 3:
        s3_key = sys.argv[2]
        list_current_metadata(s3_key)
    else:
        print("❌ Invalid command or missing arguments")
        sys.exit(1)
