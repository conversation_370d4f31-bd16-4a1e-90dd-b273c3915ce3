#!/bin/bash
# Complete Vault Setup for YendorCats Project

set -e

echo "🚀 YendorCats HashiCorp Vault Integration Setup"
echo "==============================================="
echo ""

# Check if we're in the right directory
if [ ! -f "backend/YendorCats.API/YendorCats.API.csproj" ]; then
    echo "❌ Please run this script from the project root directory"
    echo "   (where you can see the backend/ folder)"
    exit 1
fi

# Step 1: Install Vault if needed
echo "📦 Step 1: Installing HashiCorp Vault..."
if ! command -v vault &> /dev/null; then
    if [[ "$OSTYPE" == "darwin"* ]]; then
        if command -v brew &> /dev/null; then
            brew tap hashicorp/tap
            brew install hashicorp/tap/vault
        else
            echo "❌ Please install Homebrew first: https://brew.sh"
            exit 1
        fi
    else
        echo "❌ Please install Vault manually from: https://www.vaultproject.io/downloads"
        exit 1
    fi
else
    echo "✅ Vault already installed"
fi

# Step 2: Set up Vault configuration
echo ""
echo "🔧 Step 2: Setting up Vault configuration..."

mkdir -p ~/.vault-data

cat > ~/.vault-config.hcl << 'EOF'
ui = true
storage "file" {
  path = "~/.vault-data"
}
listener "tcp" {
  address = "127.0.0.1:8200"
  tls_disable = true
}
api_addr = "http://127.0.0.1:8200"
disable_mlock = true
EOF

echo "✅ Vault configuration created"

# Step 3: Start Vault
echo ""
echo "🚀 Step 3: Starting Vault server..."

export VAULT_ADDR='http://127.0.0.1:8200'

# Kill any existing Vault process
pkill vault 2>/dev/null || true
sleep 2

# Start Vault in background
vault server -config=~/.vault-config.hcl > ~/.vault-server.log 2>&1 &
VAULT_PID=$!
echo $VAULT_PID > ~/.vault-server.pid

echo "⏳ Waiting for Vault to start..."
sleep 5

# Check if Vault is running
if ! vault status > /dev/null 2>&1; then
    echo "❌ Failed to start Vault. Check ~/.vault-server.log for details"
    exit 1
fi

echo "✅ Vault is running at http://127.0.0.1:8200"

# Step 4: Initialize Vault
echo ""
echo "🔐 Step 4: Initializing Vault..."

if vault status -format=json | jq -r '.initialized' | grep -q "true"; then
    echo "✅ Vault already initialized"
    
    # Load existing credentials
    if [ -f ~/.vault-root-token.txt ]; then
        ROOT_TOKEN=$(cat ~/.vault-root-token.txt)
    else
        echo "❌ Vault is initialized but root token not found"
        echo "   Please check ~/.vault-root-token.txt"
        exit 1
    fi
else
    echo "🔧 Initializing Vault for first time..."
    
    INIT_OUTPUT=$(vault operator init -key-shares=5 -key-threshold=3 -format=json)
    
    echo "$INIT_OUTPUT" | jq -r '.unseal_keys_b64[]' > ~/.vault-unseal-keys.txt
    echo "$INIT_OUTPUT" | jq -r '.root_token' > ~/.vault-root-token.txt
    
    chmod 600 ~/.vault-unseal-keys.txt ~/.vault-root-token.txt
    
    ROOT_TOKEN=$(cat ~/.vault-root-token.txt)
    
    echo "✅ Vault initialized successfully"
fi

# Step 5: Unseal Vault
echo ""
echo "🔓 Step 5: Unsealing Vault..."

if vault status -format=json | jq -r '.sealed' | grep -q "true"; then
    head -3 ~/.vault-unseal-keys.txt | while read key; do
        vault operator unseal "$key" > /dev/null
    done
    echo "✅ Vault unsealed"
else
    echo "✅ Vault already unsealed"
fi

# Step 6: Authenticate and setup secrets
echo ""
echo "🔑 Step 6: Setting up secrets..."

vault auth "$ROOT_TOKEN" > /dev/null

# Enable KV secrets engine
if ! vault secrets list | grep -q "secret/"; then
    vault secrets enable -path=secret kv-v2
    echo "✅ KV secrets engine enabled"
fi

# Step 7: Collect B2 credentials
echo ""
echo "📝 Step 7: Collecting your Backblaze B2 credentials..."
echo ""
echo "You'll need your Backblaze B2 credentials:"
echo "1. Log into your Backblaze account"
echo "2. Go to App Keys section"
echo "3. Create a new application key if needed"
echo ""

read -p "Enter your Backblaze B2 Key ID: " B2_KEY_ID
echo ""
read -s -p "Enter your Backblaze B2 Application Key: " B2_SECRET_KEY
echo ""
echo ""

if [ -z "$B2_KEY_ID" ] || [ -z "$B2_SECRET_KEY" ]; then
    echo "❌ B2 credentials cannot be empty"
    exit 1
fi

# Step 8: Store secrets in Vault
echo "💾 Step 8: Storing secrets in Vault..."

JWT_SECRET=$(openssl rand -base64 64)

vault kv put secret/yendorcats/app-secrets \
    DbConnectionString="Server=localhost;Database=YendorCats;User=root;Password=password;Port=3306;" \
    JwtSecret="$JWT_SECRET" \
    JwtIssuer="YendorCatsApi" \
    JwtAudience="YendorCatsClients" \
    JwtExpiryMinutes=60 \
    RefreshExpiryDays=7 \
    S3AccessKey="$B2_KEY_ID" \
    S3SecretKey="$B2_SECRET_KEY" \
    S3SessionToken="" \
    ApiKey="$(openssl rand -hex 32)"

echo "✅ Secrets stored in Vault"

# Step 9: Create application token
echo ""
echo "🎫 Step 9: Creating application token..."

APP_TOKEN=$(vault token create -policy=default -ttl=8760h -format=json | jq -r '.auth.client_token')

# Step 10: Update application configuration
echo ""
echo "🔧 Step 10: Updating application configuration..."

# Update appsettings.Development.json
if [ -f "backend/YendorCats.API/appsettings.Development.json" ]; then
    # Use sed to replace the token
    sed -i.bak "s/your-vault-token-will-be-set-here/$APP_TOKEN/g" backend/YendorCats.API/appsettings.Development.json
    rm backend/YendorCats.API/appsettings.Development.json.bak
    echo "✅ Updated appsettings.Development.json"
fi

# Create management scripts
echo ""
echo "📜 Step 11: Creating management scripts..."

cat > ~/.vault-start.sh << 'EOF'
#!/bin/bash
export VAULT_ADDR='http://127.0.0.1:8200'
vault server -config=~/.vault-config.hcl > ~/.vault-server.log 2>&1 &
echo $! > ~/.vault-server.pid
echo "✅ Vault started. PID: $(cat ~/.vault-server.pid)"
echo "🌐 UI: http://127.0.0.1:8200/ui"
EOF

cat > ~/.vault-stop.sh << 'EOF'
#!/bin/bash
if [ -f ~/.vault-server.pid ]; then
    kill $(cat ~/.vault-server.pid) 2>/dev/null
    rm ~/.vault-server.pid
    echo "✅ Vault stopped"
else
    echo "⚠️  Vault PID file not found"
fi
EOF

cat > ~/.vault-status.sh << 'EOF'
#!/bin/bash
export VAULT_ADDR='http://127.0.0.1:8200'
echo "🔍 Vault Status:"
vault status
echo ""
echo "🔑 Secrets:"
vault kv get secret/yendorcats/app-secrets
EOF

chmod +x ~/.vault-start.sh ~/.vault-stop.sh ~/.vault-status.sh

# Final summary
echo ""
echo "🎉 Setup Complete!"
echo "=================="
echo ""
echo "✅ HashiCorp Vault is running and configured"
echo "✅ Your B2 credentials are securely stored"
echo "✅ Application is configured to use Vault"
echo ""
echo "📋 Summary:"
echo "  🌐 Vault UI: http://127.0.0.1:8200/ui"
echo "  🔑 Root Token: ~/.vault-root-token.txt"
echo "  🗝️  Unseal Keys: ~/.vault-unseal-keys.txt"
echo "  📊 Server Log: ~/.vault-server.log"
echo ""
echo "🔧 Management Commands:"
echo "  Start Vault:  ~/.vault-start.sh"
echo "  Stop Vault:   ~/.vault-stop.sh"
echo "  Check Status: ~/.vault-status.sh"
echo ""
echo "🚀 Next Steps:"
echo "1. Test your application: cd backend/YendorCats.API && dotnet run"
echo "2. Check Vault UI: http://127.0.0.1:8200/ui"
echo "3. Your B2 credentials are now secure!"
echo ""
echo "⚠️  IMPORTANT:"
echo "- Keep ~/.vault-root-token.txt and ~/.vault-unseal-keys.txt secure"
echo "- Never commit these files to version control"
echo "- Back up these files in a secure location"
echo ""
