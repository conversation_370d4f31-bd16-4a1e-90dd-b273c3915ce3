#!/bin/bash
# Initialize Vault and Store YendorCats Secrets

set -e

export VAULT_ADDR='http://127.0.0.1:8200'

echo "🔐 Initializing HashiCorp Vault for YendorCats"
echo "=============================================="

# Check if Vault is running
if ! vault status > /dev/null 2>&1; then
    echo "❌ Vault is not running. Please start it first:"
    echo "   ~/.vault-start.sh"
    exit 1
fi

# Check if Vault is already initialized
if vault status -format=json | jq -r '.initialized' | grep -q "true"; then
    echo "✅ Vault is already initialized"
else
    echo "🔧 Initializing Vault..."
    
    # Initialize Vault
    INIT_OUTPUT=$(vault operator init -key-shares=5 -key-threshold=3 -format=json)
    
    # Save keys and token
    echo "$INIT_OUTPUT" | jq -r '.unseal_keys_b64[]' > ~/.vault-unseal-keys.txt
    echo "$INIT_OUTPUT" | jq -r '.root_token' > ~/.vault-root-token.txt
    
    # Set secure permissions
    chmod 600 ~/.vault-unseal-keys.txt ~/.vault-root-token.txt
    
    echo "✅ Vault initialized successfully"
    echo "🔑 Unseal keys saved to: ~/.vault-unseal-keys.txt"
    echo "🎫 Root token saved to: ~/.vault-root-token.txt"
    echo ""
    echo "⚠️  IMPORTANT: Store these files securely and separately!"
fi

# Check if Vault is sealed
if vault status -format=json | jq -r '.sealed' | grep -q "true"; then
    echo "🔓 Unsealing Vault..."
    
    # Unseal Vault using first 3 keys
    head -3 ~/.vault-unseal-keys.txt | while read key; do
        vault operator unseal "$key" > /dev/null
    done
    
    echo "✅ Vault unsealed successfully"
fi

# Authenticate with root token
ROOT_TOKEN=$(cat ~/.vault-root-token.txt)
vault auth "$ROOT_TOKEN" > /dev/null

echo "🔧 Setting up secrets engine..."

# Enable KV secrets engine if not already enabled
if ! vault secrets list | grep -q "secret/"; then
    vault secrets enable -path=secret kv-v2
    echo "✅ KV secrets engine enabled"
else
    echo "✅ KV secrets engine already enabled"
fi

echo ""
echo "📝 Now let's store your Backblaze B2 credentials..."
echo ""

# Prompt for B2 credentials
read -p "Enter your Backblaze B2 Key ID: " B2_KEY_ID
read -s -p "Enter your Backblaze B2 Application Key: " B2_SECRET_KEY
echo ""

# Generate a secure JWT secret
JWT_SECRET=$(openssl rand -base64 64)

# Store secrets in Vault
echo "💾 Storing secrets in Vault..."

vault kv put secret/yendorcats/app-secrets \
    DbConnectionString="Server=localhost;Database=YendorCats;User=root;Password=password;Port=3306;" \
    JwtSecret="$JWT_SECRET" \
    JwtIssuer="YendorCatsApi" \
    JwtAudience="YendorCatsClients" \
    JwtExpiryMinutes=60 \
    RefreshExpiryDays=7 \
    S3AccessKey="$B2_KEY_ID" \
    S3SecretKey="$B2_SECRET_KEY" \
    S3SessionToken="" \
    ApiKey="$(openssl rand -hex 32)"

echo "✅ Secrets stored successfully in Vault!"

# Verify secrets were stored
echo ""
echo "🔍 Verifying stored secrets..."
if vault kv get secret/yendorcats/app-secrets > /dev/null 2>&1; then
    echo "✅ Secrets verification successful"
    
    # Show secret keys (not values)
    echo ""
    echo "📋 Stored secret keys:"
    vault kv get -format=json secret/yendorcats/app-secrets | jq -r '.data.data | keys[]' | sed 's/^/  - /'
else
    echo "❌ Failed to verify secrets"
    exit 1
fi

echo ""
echo "🎉 Vault setup complete!"
echo ""
echo "📋 Summary:"
echo "  - Vault Address: $VAULT_ADDR"
echo "  - Secrets Path: secret/yendorcats/app-secrets"
echo "  - Root Token: ~/.vault-root-token.txt"
echo "  - Unseal Keys: ~/.vault-unseal-keys.txt"
echo ""
echo "🔧 Your application is now configured to use:"
echo "  - Backblaze B2 credentials from Vault"
echo "  - Secure JWT secret"
echo "  - Database connection string"
echo ""
echo "⚠️  Remember to:"
echo "  1. Keep your unseal keys and root token secure"
echo "  2. Update your application's Vault token in appsettings.json"
echo "  3. Never commit secrets to version control"
echo ""

# Create a token for the application
echo "🎫 Creating application token..."
APP_TOKEN=$(vault token create -policy=default -ttl=8760h -format=json | jq -r '.auth.client_token')

echo "📝 Application token: $APP_TOKEN"
echo ""
echo "🔧 Update your appsettings.json with this token:"
echo '{'
echo '  "Vault": {'
echo '    "Address": "http://localhost:8200",'
echo "    \"Token\": \"$APP_TOKEN\","
echo '    "SecretPath": "secret/yendorcats/app-secrets"'
echo '  }'
echo '}'
echo ""
