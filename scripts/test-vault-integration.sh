#!/bin/bash
# Test Vault Integration with YendorCats

export VAULT_ADDR='http://127.0.0.1:8200'

echo "🧪 Testing Vault Integration"
echo "============================"
echo ""

# Test 1: Check if Vault is running
echo "1️⃣  Testing Vault connectivity..."
if vault status > /dev/null 2>&1; then
    echo "✅ Vault is running and accessible"
else
    echo "❌ Vault is not running. Start it with: ~/.vault-start.sh"
    exit 1
fi

# Test 2: Check if secrets exist
echo ""
echo "2️⃣  Testing secret retrieval..."
if vault kv get secret/yendorcats/app-secrets > /dev/null 2>&1; then
    echo "✅ Secrets are accessible"
    
    # Show secret keys (not values)
    echo "📋 Available secrets:"
    vault kv get -format=json secret/yendorcats/app-secrets | jq -r '.data.data | keys[]' | sed 's/^/  - /'
else
    echo "❌ Cannot access secrets. Run setup first: ./scripts/quick-vault-setup.sh"
    exit 1
fi

# Test 3: Test application configuration
echo ""
echo "3️⃣  Testing application configuration..."
if [ -f "backend/YendorCats.API/appsettings.Development.json" ]; then
    if grep -q "your-vault-token-will-be-set-here" backend/YendorCats.API/appsettings.Development.json; then
        echo "⚠️  Application token not set. Run: ./scripts/quick-vault-setup.sh"
    else
        echo "✅ Application configuration looks good"
    fi
else
    echo "❌ Application configuration file not found"
fi

# Test 4: Test specific B2 credentials
echo ""
echo "4️⃣  Testing B2 credentials..."
S3_ACCESS_KEY=$(vault kv get -field=S3AccessKey secret/yendorcats/app-secrets 2>/dev/null)
S3_SECRET_KEY=$(vault kv get -field=S3SecretKey secret/yendorcats/app-secrets 2>/dev/null)

if [ -n "$S3_ACCESS_KEY" ] && [ -n "$S3_SECRET_KEY" ]; then
    echo "✅ B2 credentials are stored"
    echo "   Access Key: ${S3_ACCESS_KEY:0:8}..."
    echo "   Secret Key: ${S3_SECRET_KEY:0:8}..."
else
    echo "❌ B2 credentials not found or incomplete"
fi

echo ""
echo "🎯 Integration Test Summary"
echo "=========================="

# Count successful tests
TESTS_PASSED=0

vault status > /dev/null 2>&1 && ((TESTS_PASSED++))
vault kv get secret/yendorcats/app-secrets > /dev/null 2>&1 && ((TESTS_PASSED++))
[ -f "backend/YendorCats.API/appsettings.Development.json" ] && ! grep -q "your-vault-token-will-be-set-here" backend/YendorCats.API/appsettings.Development.json && ((TESTS_PASSED++))
[ -n "$S3_ACCESS_KEY" ] && [ -n "$S3_SECRET_KEY" ] && ((TESTS_PASSED++))

echo "Tests passed: $TESTS_PASSED/4"

if [ $TESTS_PASSED -eq 4 ]; then
    echo "🎉 All tests passed! Your Vault integration is working correctly."
    echo ""
    echo "🚀 Ready to run your application:"
    echo "   cd backend/YendorCats.API"
    echo "   dotnet run"
    echo ""
    echo "🌐 Vault UI: http://127.0.0.1:8200/ui"
else
    echo "⚠️  Some tests failed. Please run the setup script:"
    echo "   ./scripts/quick-vault-setup.sh"
fi

echo ""
