#!/bin/bash
set -e

echo "Setting up Vault directory structure on Enhance Control Panel..."

# Create directory structure
mkdir -p /home/<USER>/vault/{data,config,logs,scripts,backups}

# Set permissions
chmod 755 /home/<USER>/vault/data /home/<USER>/vault/logs /home/<USER>/vault/scripts /home/<USER>/vault/backups
chmod 644 /home/<USER>/vault/config/*

echo "Vault directory structure created successfully at /home/<USER>/vault/"
echo "Please ensure that the path '/home/<USER>/vault/' is correct for your Enhance Control Panel setup. If not, modify the script with the correct domain or path before running."
