{"version": "1.0", "examples": {"CreateFileSystem": [{"input": {"Backup": true, "CreationToken": "tokenstring", "Encrypted": true, "PerformanceMode": "<PERSON><PERSON><PERSON><PERSON>", "Tags": [{"Key": "Name", "Value": "MyFileSystem"}]}, "output": {"CreationTime": "**********.0", "CreationToken": "tokenstring", "Encrypted": true, "FileSystemId": "fs-********", "LifeCycleState": "creating", "NumberOfMountTargets": 0, "OwnerId": "********8912", "PerformanceMode": "<PERSON><PERSON><PERSON><PERSON>", "SizeInBytes": {"Value": 0}, "Tags": [{"Key": "Name", "Value": "MyFileSystem"}]}, "comments": {"input": {}, "output": {}}, "description": "This operation creates a new, encrypted file system with automatic backups enabled, and the default generalpurpose performance mode.", "id": "to-create-a-new-file-system-1481840798547", "title": "To create a new file system"}], "CreateMountTarget": [{"input": {"FileSystemId": "fs-********", "SubnetId": "subnet-1234abcd"}, "output": {"FileSystemId": "fs-********", "IpAddress": "*********", "LifeCycleState": "creating", "MountTargetId": "fsmt-12340abc", "NetworkInterfaceId": "eni-cedf6789", "OwnerId": "********8912", "SubnetId": "subnet-1234abcd"}, "comments": {"input": {}, "output": {}}, "description": "This operation creates a new mount target for an EFS file system.", "id": "to-create-a-new-mount-target-1481842289329", "title": "To create a new mount target"}], "CreateTags": [{"input": {"FileSystemId": "fs-********", "Tags": [{"Key": "Name", "Value": "MyFileSystem"}]}, "comments": {}, "description": "This operation creates a new tag for an EFS file system.", "id": "to-create-a-new-tag-1481843409357", "title": "To create a new tag"}], "DeleteFileSystem": [{"input": {"FileSystemId": "fs-********"}, "comments": {}, "description": "This operation deletes an EFS file system.", "id": "to-delete-a-file-system-1481847318348", "title": "To delete a file system"}], "DeleteMountTarget": [{"input": {"MountTargetId": "fsmt-12340abc"}, "comments": {}, "description": "This operation deletes a mount target.", "id": "to-delete-a-mount-target-1481847635607", "title": "To delete a mount target"}], "DeleteTags": [{"input": {"FileSystemId": "fs-********", "TagKeys": ["Name"]}, "comments": {}, "description": "This operation deletes tags for an EFS file system.", "id": "to-delete-tags-for-an-efs-file-system-1481848189061", "title": "To delete tags for an EFS file system"}], "DescribeFileSystems": [{"input": {}, "output": {"FileSystems": [{"CreationTime": "**********.0", "CreationToken": "tokenstring", "FileSystemId": "fs-********", "LifeCycleState": "available", "Name": "MyFileSystem", "NumberOfMountTargets": 1, "OwnerId": "********8912", "PerformanceMode": "<PERSON><PERSON><PERSON><PERSON>", "SizeInBytes": {"Value": 6144}, "Tags": [{"Key": "Name", "Value": "MyFileSystem"}]}]}, "comments": {}, "description": "This operation describes all of the EFS file systems in an account.", "id": "to-describe-an-efs-file-system-*************", "title": "To describe an EFS file system"}], "DescribeLifecycleConfiguration": [{"input": {"FileSystemId": "fs-********"}, "output": {"LifecyclePolicies": [{"TransitionToIA": "AFTER_30_DAYS"}]}, "comments": {"input": {}, "output": {}}, "description": "This operation describes a file system's LifecycleConfiguration. EFS lifecycle management uses the LifecycleConfiguration object to identify which files to move to the EFS Infrequent Access (IA) storage class. ", "id": "to-describe-the-lifecycle-configuration-for-a-file-system-*************", "title": "To describe the lifecycle configuration for a file system"}], "DescribeMountTargetSecurityGroups": [{"input": {"MountTargetId": "fsmt-12340abc"}, "output": {"SecurityGroups": ["sg-4567abcd"]}, "comments": {}, "description": "This operation describes all of the security groups for a file system's mount target.", "id": "to-describe-the-security-groups-for-a-mount-target-********17823", "title": "To describe the security groups for a mount target"}], "DescribeMountTargets": [{"input": {"FileSystemId": "fs-********"}, "output": {"MountTargets": [{"FileSystemId": "fs-********", "IpAddress": "*********", "LifeCycleState": "available", "MountTargetId": "fsmt-12340abc", "NetworkInterfaceId": "eni-cedf6789", "OwnerId": "********8912", "SubnetId": "subnet-1234abcd"}]}, "comments": {}, "description": "This operation describes all of a file system's mount targets.", "id": "to-describe-the-mount-targets-for-a-file-system-1481849958584", "title": "To describe the mount targets for a file system"}], "DescribeTags": [{"input": {"FileSystemId": "fs-********"}, "output": {"Tags": [{"Key": "Name", "Value": "MyFileSystem"}]}, "comments": {}, "description": "This operation describes all of a file system's tags.", "id": "to-describe-the-tags-for-a-file-system-1481850497090", "title": "To describe the tags for a file system"}], "ModifyMountTargetSecurityGroups": [{"input": {"MountTargetId": "fsmt-12340abc", "SecurityGroups": ["sg-abcd1234"]}, "comments": {}, "description": "This operation modifies the security groups associated with a mount target for a file system.", "id": "to-modify-the-security-groups-associated-with-a-mount-target-for-a-file-system-1481850772562", "title": "To modify the security groups associated with a mount target for a file system"}], "PutLifecycleConfiguration": [{"input": {"FileSystemId": "fs-********", "LifecyclePolicies": [{"TransitionToIA": "AFTER_30_DAYS"}]}, "output": {"LifecyclePolicies": [{"TransitionToIA": "AFTER_30_DAYS"}]}, "comments": {"input": {}, "output": {}}, "description": "This operation enables lifecycle management on a file system by creating a new LifecycleConfiguration object. A LifecycleConfiguration object defines when files in an Amazon EFS file system are automatically transitioned to the lower-cost EFS Infrequent Access (IA) storage class. A LifecycleConfiguration applies to all files in a file system.", "id": "creates-a-new-lifecycleconfiguration-object-for-a-file-system-1551201594692", "title": "Creates a new lifecycleconfiguration object for a file system"}]}}