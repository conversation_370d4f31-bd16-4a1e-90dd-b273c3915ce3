# YendorCats Vault Integration Scripts

This directory contains scripts for setting up and managing HashiCorp Vault integration with your YendorCats project.

## 🚀 Quick Start

**Run this single command to set up everything:**

```bash
./scripts/quick-vault-setup.sh
```

## 📜 Available Scripts

### **quick-vault-setup.sh**
Complete setup script that:
- Installs HashiCorp Vault (if needed)
- Configures and starts Vault server
- Initializes and unseals Vault
- Prompts for your Backblaze B2 credentials
- Stores all secrets securely in Vault
- Updates your application configuration
- Creates management scripts

### **test-vault-integration.sh**
Tests your Vault integration:
- Verifies Vault connectivity
- Checks secret accessibility
- Validates application configuration
- Tests B2 credential storage

### **setup-vault-dev.sh**
Development-focused Vault setup:
- Installs Vault for development
- Creates basic configuration
- Sets up start/stop scripts

### **setup-vault-secrets.sh**
Secrets management script:
- Initializes Vault
- Sets up KV secrets engine
- Stores YendorCats application secrets

### **setup-secure-backup.sh**
Sets up secure backup procedures:
- Creates backup user and permissions
- Generates GPG encryption keys
- Installs backup, verification, and restore scripts
- Configures automated daily backups

## 🔧 Management Commands

After running the setup, these commands are available:

```bash
# Start Vault
~/.vault-start.sh

# Stop Vault
~/.vault-stop.sh

# Check status and view secrets
~/.vault-status.sh
```

## 📋 Prerequisites

- **macOS**: Homebrew installed
- **Linux**: apt package manager
- **Tools**: curl, jq, openssl
- **Permissions**: sudo access for installation

## 🔐 Security Notes

- Keep `~/.vault-root-token.txt` and `~/.vault-unseal-keys.txt` secure
- Never commit these files to version control
- Back up these files in a secure location
- Use different tokens for production

## 🆘 Troubleshooting

If something goes wrong:

1. **Check Vault status**: `vault status`
2. **View logs**: `cat ~/.vault-server.log`
3. **Re-run setup**: `./scripts/quick-vault-setup.sh`
4. **Test integration**: `./scripts/test-vault-integration.sh`

## 📚 Documentation

See the complete guide: [YendorCats Vault Integration Guide](../docs/YendorCats-Vault-Integration-Guide.md)
