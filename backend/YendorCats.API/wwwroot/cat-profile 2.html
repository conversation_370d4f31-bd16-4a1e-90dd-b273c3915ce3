<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cat Profile - Yendor Cats</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/cat-profile.css">
    <link rel="icon" type="image/x-icon" href="images/favicon.ico">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <img src="images/logo_shield.webp" alt="Yendor Cats Logo" class="logo-image">
                <span class="logo-text">Yendor Cats</span>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="index.html" class="nav-link">Home</a>
                </li>
                <li class="nav-item">
                    <a href="index.html#gallery" class="nav-link">Gallery</a>
                </li>
                <li class="nav-item">
                    <a href="upload.html" class="nav-link">Upload</a>
                </li>
                <li class="nav-item">
                    <a href="profiles.html" class="nav-link">Cat Profiles</a>
                </li>
                <li class="nav-item">
                    <a href="index.html#contact" class="nav-link">Contact</a>
                </li>
            </ul>
            <div class="hamburger">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <main class="main-content">
        <!-- Loading State -->
        <div class="loading-container" id="loading-container">
            <div class="loading-spinner"></div>
            <p>Loading cat profile...</p>
        </div>

        <!-- Error State -->
        <div class="error-container" id="error-container" style="display: none;">
            <div class="error-content">
                <h2>Cat Not Found</h2>
                <p>The requested cat profile could not be found.</p>
                <a href="profiles.html" class="btn btn-primary">View All Profiles</a>
            </div>
        </div>

        <!-- Cat Profile Content -->
        <div class="profile-container" id="profile-container" style="display: none;">
            <!-- Profile Header -->
            <section class="profile-header">
                <div class="container">
                    <div class="profile-header-content">
                        <div class="profile-image-container">
                            <img id="profile-image" src="" alt="" class="profile-image">
                            <div class="profile-image-placeholder" id="profile-image-placeholder">
                                <span class="placeholder-icon">🐱</span>
                            </div>
                        </div>
                        <div class="profile-info">
                            <h1 id="cat-name" class="cat-name">Loading...</h1>
                            <div class="cat-details" id="cat-details">
                                <!-- Details will be populated by JavaScript -->
                            </div>
                            <div class="profile-actions">
                                <a href="#" id="upload-photo-btn" class="btn btn-primary">Upload Photo</a>
                                <button id="share-profile-btn" class="btn btn-secondary">Share Profile</button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Profile Stats -->
            <section class="profile-stats">
                <div class="container">
                    <div class="stats-grid">
                        <div class="stat-item">
                            <span class="stat-number" id="photo-count">0</span>
                            <span class="stat-label">Photos</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number" id="age-display">Unknown</span>
                            <span class="stat-label">Age</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number" id="first-photo-date">-</span>
                            <span class="stat-label">First Photo</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number" id="latest-photo-date">-</span>
                            <span class="stat-label">Latest Photo</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Photo Timeline -->
            <section class="photo-timeline">
                <div class="container">
                    <div class="timeline-header">
                        <h2>Photo Timeline</h2>
                        <div class="timeline-controls">
                            <select id="sort-order" class="sort-select">
                                <option value="date-desc">Newest First</option>
                                <option value="date-asc">Oldest First</option>
                            </select>
                            <div class="view-toggle">
                                <button class="view-btn active" data-view="grid">Grid</button>
                                <button class="view-btn" data-view="timeline">Timeline</button>
                            </div>
                        </div>
                    </div>

                    <!-- Grid View -->
                    <div class="photos-grid" id="photos-grid">
                        <!-- Photos will be populated by JavaScript -->
                    </div>

                    <!-- Timeline View -->
                    <div class="timeline-view" id="timeline-view" style="display: none;">
                        <!-- Timeline will be populated by JavaScript -->
                    </div>

                    <!-- Empty State -->
                    <div class="empty-state" id="empty-state" style="display: none;">
                        <div class="empty-content">
                            <span class="empty-icon">📷</span>
                            <h3>No Photos Yet</h3>
                            <p>This cat doesn't have any photos uploaded yet.</p>
                            <a href="#" id="upload-first-photo-btn" class="btn btn-primary">Upload First Photo</a>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- Photo Modal -->
    <div class="photo-modal" id="photo-modal">
        <div class="modal-overlay" id="modal-overlay"></div>
        <div class="modal-content">
            <button class="modal-close" id="modal-close">&times;</button>
            <div class="modal-image-container">
                <img id="modal-image" src="" alt="">
                <div class="modal-nav">
                    <button class="nav-btn prev-btn" id="prev-photo">‹</button>
                    <button class="nav-btn next-btn" id="next-photo">›</button>
                </div>
            </div>
            <div class="modal-info">
                <h3 id="modal-photo-title"></h3>
                <div id="modal-photo-details"></div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Yendor Cats</h3>
                    <p>Breeding exceptional Maine Coon cats with love and care.</p>
                </div>
                <div class="footer-section">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="index.html#gallery">Gallery</a></li>
                        <li><a href="upload.html">Upload</a></li>
                        <li><a href="profiles.html">Cat Profiles</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Contact</h3>
                    <p>Email: <EMAIL></p>
                    <p>Phone: 0417281675</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Yendor Cats. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="js/navbar.js"></script>
    <script src="js/cat-profile.js"></script>
    <script src="js/live-reload.js"></script>
</body>
</html>
