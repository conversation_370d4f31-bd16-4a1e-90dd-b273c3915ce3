/**
 * This script provides placeholder functionality for missing images
 * during development.
 */

// Wait for the DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Add error event listeners to all images
    const images = document.querySelectorAll('img');
    
    images.forEach(img => {
        img.addEventListener('error', function() {
            // Set a placeholder for the image that failed to load
            this.src = 'data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%22400%22%20height%3D%22300%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cg%3E%3Crect%20width%3D%22400%22%20height%3D%22300%22%20fill%3D%22%23CCCCCC%22%2F%3E%3Ctext%20x%3D%22200%22%20y%3D%22150%22%20font-size%3D%2220%22%20text-anchor%3D%22middle%22%20fill%3D%22%23333333%22%3EPlaceholder%20Image%3C%2Ftext%3E%3Ctext%20x%3D%22200%22%20y%3D%22180%22%20font-size%3D%2216%22%20text-anchor%3D%22middle%22%20fill%3D%22%23333333%22%3E%28' + this.src.split('/').pop() + '%29%3C%2Ftext%3E%3C%2Fg%3E%3C%2Fsvg%3E';
            this.alt = 'Placeholder for ' + this.src.split('/').pop();
        });
    });
    
    console.log('Placeholder image handler initialized');
}); 