document.addEventListener('DOMContentLoaded', function() {
    const galleryContainer = document.getElementById('gallery-container');
    const galleryLoader = document.getElementById('gallery-loader');
    const galleryFilter = document.querySelector('.gallery-filter');
    const galleryModal = document.getElementById('gallery-modal');
    const modalImage = document.getElementById('modal-image');
    const modalCaption = document.getElementById('modal-caption');
    const modalClose = document.querySelector('.modal-close');
    const modalPrev = document.getElementById('modal-prev');
    const modalNext = document.getElementById('modal-next');

    if (!galleryContainer) return;

    const apiBaseUrl = '/api/PublicGallery';
    let allPhotos = [];
    let currentItems = [];
    let currentIndex = 0;

    async function initGallery() {
        try {
            if (galleryLoader) galleryLoader.style.display = 'block';

            const categories = ['studs', 'queens', 'kittens', 'gallery'];
            const photoPromises = categories.map(category =>
                fetch(`${apiBaseUrl}/category/${category}`).then(res => res.json())
            );

            const photoResults = await Promise.all(photoPromises);

            allPhotos = photoResults.flat();

            if (galleryLoader) galleryLoader.style.display = 'none';

            addFilterButtons(categories);
            renderGalleryItems(allPhotos);
            initModal();
        } catch (error) {
            console.error('Error initializing gallery:', error);
            if (galleryLoader) galleryLoader.innerHTML = 'Failed to load gallery.';
        }
    }

    function addFilterButtons(categories) {
        if (!galleryFilter) return;

        const allButton = document.createElement('button');
        allButton.classList.add('filter-btn', 'active');
        allButton.setAttribute('data-filter', 'all');
        allButton.textContent = 'All';
        allButton.addEventListener('click', () => filterGallery('all'));
        galleryFilter.appendChild(allButton);

        categories.forEach(category => {
            const button = document.createElement('button');
            button.classList.add('filter-btn');
            button.setAttribute('data-filter', category);
            button.textContent = formatCategoryName(category);
            button.addEventListener('click', () => filterGallery(category));
            galleryFilter.appendChild(button);
        });
    }

    function formatCategoryName(category) {
        return category
            .split('-')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ');
    }

    function filterGallery(category) {
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.classList.toggle('active', btn.getAttribute('data-filter') === category);
        });

        if (category === 'all') {
            currentItems = allPhotos;
        } else {
            currentItems = allPhotos.filter(photo => photo.category && photo.category.toLowerCase() === category.toLowerCase());
        }
        renderGalleryItems(currentItems);
    }

    function renderGalleryItems(items) {
        galleryContainer.innerHTML = '';

        if (items.length === 0) {
            galleryContainer.innerHTML = '<p>No photos found for this category.</p>';
            return;
        }

        items.forEach((item, index) => {
            const galleryItem = document.createElement('div');
            galleryItem.classList.add('gallery-item');
            galleryItem.setAttribute('data-id', item.id || index);

            const displayName = getCatDisplayName(item);
            const ageText = item.age ? `${item.age} years old` : '';

            galleryItem.innerHTML = `
                <img src="${item.imageUrl}" alt="${displayName}" loading="lazy">
                <div class="gallery-item-overlay">
                    <h4>${displayName}</h4>
                    <p>${ageText}</p>
                    <p class="description">${item.description || ''}</p>
                </div>
            `;

            galleryItem.addEventListener('click', function() {
                openModal(index, items);
            });

            galleryContainer.appendChild(galleryItem);
        });
    }

    function initModal() {
        if (!galleryModal) return;

        if (modalClose) {
            modalClose.addEventListener('click', closeModal);
        }

        galleryModal.addEventListener('click', function(e) {
            if (e.target === galleryModal) {
                closeModal();
            }
        });

        document.addEventListener('keydown', function(e) {
            if (!galleryModal.style.display || galleryModal.style.display === 'none') return;

            if (e.key === 'Escape') {
                closeModal();
            } else if (e.key === 'ArrowLeft') {
                showPrevItem();
            } else if (e.key === 'ArrowRight') {
                showNextItem();
            }
        });

        if (modalPrev) {
            modalPrev.addEventListener('click', showPrevItem);
        }

        if (modalNext) {
            modalNext.addEventListener('click', showNextItem);
        }
    }

    function openModal(index, items) {
        currentIndex = index;
        currentItems = items;

        const item = items[index];
        const displayName = getCatDisplayName(item);

        if (modalImage) {
            modalImage.src = item.imageUrl;
            modalImage.alt = displayName;
        }

        if (modalCaption) {
            let captionHTML = `<h4>${displayName}</h4>`;
            if (item.description) {
                captionHTML += `<p class="description">${item.description}</p>`;
            }

            captionHTML += '<div class="metadata-section">';
            if (item.age) captionHTML += `<p><strong>Age:</strong> ${item.age} years</p>`;
            if (item.gender) captionHTML += `<p><strong>Gender:</strong> ${item.gender === 'M' ? 'Male' : 'Female'}</p>`;
            if (item.breed) captionHTML += `<p><strong>Breed:</strong> ${item.breed}</p>`;
            if (item.color) captionHTML += `<p><strong>Hair Color:</strong> ${item.color}</p>`;
            if (item.eyeColor) captionHTML += `<p><strong>Eye Color:</strong> ${item.eyeColor}</p>`;
            if (item.markings) captionHTML += `<p><strong>Markings:</strong> ${item.markings}</p>`;
            if (item.bloodline) captionHTML += `<p><strong>Bloodline:</strong> ${item.bloodline}</p>`;
            if (item.tags) captionHTML += `<p><strong>Tags:</strong> ${item.tags}</p>`;
            if (item.dateTaken) captionHTML += `<p><strong>Date Taken:</strong> ${new Date(item.dateTaken).toLocaleDateString()}</p>`;
            captionHTML += '</div>';

            modalCaption.innerHTML = captionHTML;
        }

        galleryModal.style.display = 'block';
        document.body.style.overflow = 'hidden';
    }

    function closeModal() {
        galleryModal.style.display = 'none';
        document.body.style.overflow = '';
    }

    function showPrevItem() {
        currentIndex = (currentIndex - 1 + currentItems.length) % currentItems.length;
        openModal(currentIndex, currentItems);
    }

    function showNextItem() {
        currentIndex = (currentIndex + 1) % currentItems.length;
        openModal(currentIndex, currentItems);
    }

    /**
     * Get display name for a cat image
     * @param {Object} image - Image object with catName and imageUrl
     * @returns {string} - Display name for the cat
     */
    function getCatDisplayName(image) {
        // If IPTC metadata provided a cat name, use it
        if (image.catName && image.catName.trim() !== '') {
            return image.catName.trim();
        }

        // Extract cat name from URL path
        // URL format: https://f004.backblazeb2.com/file/yendor/studs/louie/image.jpg
        try {
            const urlParts = image.imageUrl.split('/');
            const pathIndex = urlParts.indexOf('yendor');

            if (pathIndex !== -1 && pathIndex + 2 < urlParts.length) {
                // Get the cat name from the folder structure
                // URL structure: .../yendor/category/catname/image.jpg
                const catName = urlParts[pathIndex + 3]; // cat name folder

                if (catName && catName !== '.bzEmpty' && !catName.includes('.')) {
                    // Capitalize first letter and return
                    return catName.charAt(0).toUpperCase() + catName.slice(1);
                }
            }
        } catch (error) {
            console.warn('Error extracting cat name from URL:', error);
        }

        // Fallback to generic name
        return 'Maine Coon Cat';
    }

    initGallery();
});
