/**
 * Carousel Component for Yendor Cats
 * Features:
 * - Auto-scrolling
 * - Manual navigation
 * - Progress indicator
 * - Supports both single and multi-item views
 * - Touch swipe support for mobile
 * - Lightbox/popup for images
 */

class Carousel {
    /**
     * Initialize a new carousel
     * @param {HTMLElement} element - The carousel element
     * @param {Object} options - Carousel options
     */
    constructor(element, options = {}) {
        // DOM Elements
        this.carousel = element;
        this.container = this.carousel.querySelector('.carousel-container');
        
        // Clear any existing slides with loading placeholder
        const loadingPlaceholder = this.container.querySelector('.loading-placeholder');
        if (loadingPlaceholder) {
            console.log('Removing loading placeholder from carousel');
            loadingPlaceholder.remove();
        }
        
        // Get updated slides collection after any dynamic content changes
        this.slides = this.carousel.querySelectorAll('.carousel-slide');
        console.log(`Carousel constructor found ${this.slides.length} slides`);
        
        // Default Options
        this.options = {
            autoplay: true,
            autoplaySpeed: 5000, // ms
            slideDuration: 500, // ms
            pauseOnHover: true,
            infinite: true,
            indicators: true,
            navigation: true,
            ...options
        };
        
        // State
        this.currentIndex = 0;
        this.slideCount = this.slides.length;
        this.autoplayTimer = null;
        this.progressBar = null;
        this.isTransitioning = false;
        this.touchStartX = 0;
        this.touchEndX = 0;
        
        console.log(`Carousel constructor: slideCount = ${this.slideCount}`);
        
        // Initialize
        this.init();
    }
    
    /**
     * Initialize the carousel
     */
    init() {
        console.log('Initializing carousel with the following structure:');
        console.log('- Carousel element:', this.carousel);
        console.log('- Container element:', this.container);
        
        // Ensure slides are refreshed from DOM
        this.slides = this.carousel.querySelectorAll('.carousel-slide');
        this.slideCount = this.slides.length;
        
        console.log(`- Found ${this.slideCount} slides:`, this.slides);
        
        // Skip if no slides
        if (this.slideCount === 0) {
            console.error('❌ No slides found, carousel initialization aborted');
            return;
        }
        
        // Ensure container is displayed correctly
        if (this.container) {
            this.container.style.display = 'flex';
            this.container.style.transition = 'transform 0.5s ease';
            console.log('✓ Set container display to flex');
        }
        
        // Remove any existing controls
        this.removeExistingControls();
        console.log('✓ Removed any existing controls');
        
        // Always create navigation buttons - this was the issue
        this.createNavigation();
        console.log('✓ Created navigation buttons');
        
        // Create indicators if enabled
        if (this.options.indicators) {
            this.createIndicators();
            console.log('✓ Created indicators');
        }
        
        // Create progress bar for autoplay
        if (this.options.autoplay) {
            this.createProgressBar();
            console.log('✓ Created progress bar');
        }
        
        // Set up event listeners
        this.setupEventListeners();
        console.log('✓ Set up event listeners');
        
        // Reset to first slide
        this.currentIndex = 0;
        this.goToSlide(0);
        console.log('✓ Reset to first slide');
        
        // Start autoplay if enabled
        if (this.options.autoplay) {
            this.startAutoplay();
            console.log('✓ Started autoplay');
        }
        
        console.log('Carousel initialization complete');
    }
    
    /**
     * Remove existing navigation and indicators
     */
    removeExistingControls() {
        // Remove existing buttons
        const existingButtons = this.carousel.querySelectorAll('.carousel-button');
        existingButtons.forEach(button => button.remove());
        
        // Remove existing indicators
        const existingIndicators = this.carousel.querySelector('.carousel-indicators');
        if (existingIndicators) {
            existingIndicators.remove();
        }
        
        // Remove existing slide counter
        const existingSlideCounter = this.carousel.querySelector('.carousel-slide-counter');
        if (existingSlideCounter) {
            existingSlideCounter.remove();
        }
        
        // Remove existing progress bar
        const existingProgress = this.carousel.querySelector('.carousel-progress');
        if (existingProgress) {
            existingProgress.remove();
        }
    }
    
    /**
     * Create navigation buttons
     */
    createNavigation() {
        // Create prev button
        const prevButton = document.createElement('button');
        prevButton.className = 'carousel-button prev';
        prevButton.setAttribute('aria-label', 'Previous slide');
        prevButton.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="15 18 9 12 15 6"></polyline>
            </svg>
        `;
        
        // Create next button
        const nextButton = document.createElement('button');
        nextButton.className = 'carousel-button next';
        nextButton.setAttribute('aria-label', 'Next slide');
        nextButton.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="9 18 15 12 9 6"></polyline>
            </svg>
        `;
        
        // Add buttons to carousel
        this.carousel.appendChild(prevButton);
        this.carousel.appendChild(nextButton);
        
        // Store references
        this.prevButton = prevButton;
        this.nextButton = nextButton;
    }
    
    /**
     * Create slide indicators
     */
    createIndicators() {
        const indicators = document.createElement('div');
        indicators.className = 'carousel-indicators';
        
        // Add a text indicator showing current slide / total slides
        const slideCounter = document.createElement('div');
        slideCounter.className = 'carousel-slide-counter';
        slideCounter.innerHTML = `<span>1</span>/<span>${this.slideCount}</span>`;
        this.carousel.appendChild(slideCounter);
        this.slideCounter = slideCounter;
        
        for (let i = 0; i < this.slideCount; i++) {
            const indicator = document.createElement('button');
            indicator.className = 'carousel-indicator';
            indicator.setAttribute('aria-label', `Go to slide ${i + 1}`);
            indicator.dataset.index = i;
            indicators.appendChild(indicator);
        }
        
        this.carousel.appendChild(indicators);
        this.indicators = this.carousel.querySelectorAll('.carousel-indicator');
        this.updateIndicators();
    }
    
    /**
     * Create progress bar for autoplay
     */
    createProgressBar() {
        const progressBar = document.createElement('div');
        progressBar.className = 'carousel-progress';
        this.carousel.appendChild(progressBar);
        this.progressBar = progressBar;
    }
    
    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Navigation buttons - always add these listeners since we always create the buttons
        this.prevButton.addEventListener('click', () => this.prev());
        this.nextButton.addEventListener('click', () => this.next());
        
        // Indicators
        if (this.options.indicators && this.indicators) {
            this.indicators.forEach(indicator => {
                indicator.addEventListener('click', () => {
                    const index = parseInt(indicator.dataset.index);
                    this.goToSlide(index);
                });
            });
        }
        
        // Pause on hover
        if (this.options.pauseOnHover && this.options.autoplay) {
            this.carousel.addEventListener('mouseenter', () => this.pauseAutoplay());
            this.carousel.addEventListener('mouseleave', () => this.startAutoplay());
        }
        
        // Touch events for swipe
        this.carousel.addEventListener('touchstart', e => this.handleTouchStart(e), { passive: true });
        this.carousel.addEventListener('touchmove', e => this.handleTouchMove(e), { passive: true });
        this.carousel.addEventListener('touchend', () => this.handleTouchEnd());
        
        // Window resize to handle responsive adjustments
        window.addEventListener('resize', () => this.goToSlide(this.currentIndex));
    }
    
    /**
     * Handle touch start event
     * @param {TouchEvent} e - Touch event
     */
    handleTouchStart(e) {
        this.touchStartX = e.touches[0].clientX;
        this.pauseAutoplay();
    }
    
    /**
     * Handle touch move event
     * @param {TouchEvent} e - Touch event
     */
    handleTouchMove(e) {
        this.touchEndX = e.touches[0].clientX;
    }
    
    /**
     * Handle touch end event
     */
    handleTouchEnd() {
        const difference = this.touchStartX - this.touchEndX;
        const threshold = 50; // minimum swipe distance
        
        if (difference > threshold) {
            this.next();
        } else if (difference < -threshold) {
            this.prev();
        }
        
        if (this.options.autoplay) {
            this.startAutoplay();
        }
    }
    
    /**
     * Go to previous slide
     */
    prev() {
        if (this.isTransitioning) return;
        
        let prevIndex = this.currentIndex - 1;
        if (prevIndex < 0) {
            prevIndex = this.options.infinite ? this.slideCount - 1 : 0;
        }
        
        this.goToSlide(prevIndex);
    }
    
    /**
     * Go to next slide
     */
    next() {
        if (this.isTransitioning) return;
        
        let nextIndex = this.currentIndex + 1;
        if (nextIndex >= this.slideCount) {
            nextIndex = this.options.infinite ? 0 : this.slideCount - 1;
        }
        
        this.goToSlide(nextIndex);
    }
    
    /**
     * Go to specific slide
     * @param {number} index - Slide index
     */
    goToSlide(index) {
        if (this.isTransitioning) return;
        
        // Ensure valid index
        if (index < 0) index = 0;
        if (index >= this.slideCount) index = this.slideCount - 1;
        
        this.isTransitioning = true;
        
        // Update current index
        this.currentIndex = index;
        
        // Get width of the carousel
        const carouselWidth = this.carousel.offsetWidth;
        const position = -index * carouselWidth;
        
        console.log(`Going to slide ${index + 1}/${this.slideCount}, position: ${position}px, carousel width: ${carouselWidth}px`);
        
        // Update transform position
        this.container.style.transform = `translateX(${position}px)`;
        
        // Update indicators
        this.updateIndicators();
        
        // Reset autoplay
        if (this.options.autoplay) {
            this.resetAutoplay();
        }
        
        // Clear transitioning state after animation completes
        setTimeout(() => {
            this.isTransitioning = false;
        }, this.options.slideDuration);
    }
    
    /**
     * Update indicators
     */
    updateIndicators() {
        if (!this.options.indicators) return;
        
        this.indicators.forEach((indicator, index) => {
            if (index === this.currentIndex) {
                indicator.classList.add('active');
            } else {
                indicator.classList.remove('active');
            }
        });
        
        // Update the slide counter
        if (this.slideCounter) {
            this.slideCounter.innerHTML = `<span>${this.currentIndex + 1}</span>/<span>${this.slideCount}</span>`;
        }
    }
    
    /**
     * Start autoplay
     */
    startAutoplay() {
        if (!this.options.autoplay) return;
        
        clearInterval(this.autoplayTimer);
        this.autoplayTimer = setInterval(() => {
            this.next();
        }, this.options.autoplaySpeed);
        
        this.startProgressBar();
    }
    
    /**
     * Pause autoplay
     */
    pauseAutoplay() {
        clearInterval(this.autoplayTimer);
        this.stopProgressBar();
    }
    
    /**
     * Reset autoplay timer
     */
    resetAutoplay() {
        this.pauseAutoplay();
        this.startAutoplay();
    }
    
    /**
     * Start progress bar animation
     */
    startProgressBar() {
        if (!this.progressBar) return;
        
        this.progressBar.style.width = '0%';
        this.progressBar.style.transition = `width ${this.options.autoplaySpeed}ms linear`;
        
        // Force reflow
        this.progressBar.offsetHeight;
        
        this.progressBar.style.width = '100%';
    }
    
    /**
     * Stop progress bar animation
     */
    stopProgressBar() {
        if (!this.progressBar) return;
        
        const computedStyle = window.getComputedStyle(this.progressBar);
        const width = parseFloat(computedStyle.getPropertyValue('width'));
        const totalWidth = parseFloat(computedStyle.getPropertyValue('width'));
        
        this.progressBar.style.transition = 'none';
        this.progressBar.style.width = `${width}px`;
    }
    
    /**
     * Setup lightbox/popup for images
     */
    setupLightbox() {
        // Find all images in carousel
        const carouselImages = this.carousel.querySelectorAll('img');
        
        // Skip if no images
        if (carouselImages.length === 0) return;
        
        // Create lightbox if it doesn't exist yet
        if (!document.querySelector('.lightbox')) {
            this.createLightbox();
        }
        
        // Get lightbox elements
        const lightbox = document.querySelector('.lightbox');
        const lightboxImage = lightbox.querySelector('.lightbox-image');
        const closeButton = lightbox.querySelector('.lightbox-close');
        const prevButton = lightbox.querySelector('.lightbox-nav-button.prev');
        const nextButton = lightbox.querySelector('.lightbox-nav-button.next');
        
        // Current image index for lightbox
        let currentLightboxIndex = 0;
        
        // All images on the page for lightbox navigation (not just this carousel)
        const allImages = Array.from(document.querySelectorAll('.carousel img'));
        
        // Add click event to each image
        carouselImages.forEach((img, index) => {
            img.addEventListener('click', () => {
                // Find this image's index in all images
                const globalIndex = allImages.indexOf(img);
                
                // Set current index
                currentLightboxIndex = globalIndex;
                
                // Display image in lightbox
                lightboxImage.src = img.src;
                lightbox.classList.add('active');
                
                // Disable page scrolling
                document.body.style.overflow = 'hidden';
            });
        });
        
        // Close lightbox on click
        closeButton.addEventListener('click', () => {
            lightbox.classList.remove('active');
            
            // Enable page scrolling
            document.body.style.overflow = '';
        });
        
        // Navigate to previous image
        prevButton.addEventListener('click', () => {
            currentLightboxIndex = (currentLightboxIndex - 1 + allImages.length) % allImages.length;
            lightboxImage.src = allImages[currentLightboxIndex].src;
        });
        
        // Navigate to next image
        nextButton.addEventListener('click', () => {
            currentLightboxIndex = (currentLightboxIndex + 1) % allImages.length;
            lightboxImage.src = allImages[currentLightboxIndex].src;
        });
        
        // Close lightbox on escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                lightbox.classList.remove('active');
                document.body.style.overflow = '';
            } else if (e.key === 'ArrowLeft') {
                if (lightbox.classList.contains('active')) {
                    currentLightboxIndex = (currentLightboxIndex - 1 + allImages.length) % allImages.length;
                    lightboxImage.src = allImages[currentLightboxIndex].src;
                }
            } else if (e.key === 'ArrowRight') {
                if (lightbox.classList.contains('active')) {
                    currentLightboxIndex = (currentLightboxIndex + 1) % allImages.length;
                    lightboxImage.src = allImages[currentLightboxIndex].src;
                }
            }
        });
        
        // Close lightbox when clicking outside the image
        lightbox.addEventListener('click', (e) => {
            if (e.target === lightbox) {
                lightbox.classList.remove('active');
                document.body.style.overflow = '';
            }
        });
    }
    
    /**
     * Create lightbox/popup for images
     */
    createLightbox() {
        const lightbox = document.createElement('div');
        lightbox.className = 'lightbox';
        
        lightbox.innerHTML = `
            <div class="lightbox-content">
                <img class="lightbox-image" src="" alt="Enlarged view">
                <button class="lightbox-close" aria-label="Close lightbox">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>
            <div class="lightbox-nav">
                <button class="lightbox-nav-button prev" aria-label="Previous image">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="15 18 9 12 15 6"></polyline>
                    </svg>
                </button>
                <button class="lightbox-nav-button next" aria-label="Next image">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="9 18 15 12 9 6"></polyline>
                    </svg>
                </button>
            </div>
        `;
        
        document.body.appendChild(lightbox);
    }
    
    /**
     * Force refresh the carousel with the current DOM state
     * This can be called after dynamically adding/removing slides
     */
    forceRefresh() {
        console.log('Force refreshing carousel');
        
        // Re-query all slides
        this.slides = this.carousel.querySelectorAll('.carousel-slide');
        this.slideCount = this.slides.length;
        
        console.log(`Carousel refreshed with ${this.slideCount} slides`);
        
        // Recreate indicators
        if (this.options.indicators) {
            const existingIndicators = this.carousel.querySelector('.carousel-indicators');
            if (existingIndicators) {
                existingIndicators.remove();
            }
            
            const existingSlideCounter = this.carousel.querySelector('.carousel-slide-counter');
            if (existingSlideCounter) {
                existingSlideCounter.remove();
            }
            
            this.createIndicators();
        }
        
        // Reset to first slide
        this.currentIndex = 0;
        this.goToSlide(0);
        
        // Restart autoplay if enabled
        if (this.options.autoplay) {
            this.resetAutoplay();
        }
    }
}


// Export for use in other scripts
if (typeof module !== 'undefined') {
    module.exports = { Carousel };
}
