// Cat Profile Page Functionality
class CatProfile {
    constructor() {
        this.apiBaseUrl = '/api/PhotoUpload';
        this.catName = this.getCatNameFromUrl();
        this.currentPhotos = [];
        this.currentPhotoIndex = 0;
        this.currentView = 'grid';
        this.currentSort = 'date-desc';
        
        this.initializeElements();
        this.bindEvents();
        this.loadCatProfile();
    }

    initializeElements() {
        this.loadingContainer = document.getElementById('loading-container');
        this.errorContainer = document.getElementById('error-container');
        this.profileContainer = document.getElementById('profile-container');
        
        this.catNameElement = document.getElementById('cat-name');
        this.catDetailsElement = document.getElementById('cat-details');
        this.profileImage = document.getElementById('profile-image');
        this.profileImagePlaceholder = document.getElementById('profile-image-placeholder');
        
        this.photoCountElement = document.getElementById('photo-count');
        this.ageDisplayElement = document.getElementById('age-display');
        this.firstPhotoDateElement = document.getElementById('first-photo-date');
        this.latestPhotoDateElement = document.getElementById('latest-photo-date');
        
        this.photosGrid = document.getElementById('photos-grid');
        this.timelineView = document.getElementById('timeline-view');
        this.emptyState = document.getElementById('empty-state');
        
        this.sortSelect = document.getElementById('sort-order');
        this.viewButtons = document.querySelectorAll('.view-btn');
        
        this.uploadPhotoBtn = document.getElementById('upload-photo-btn');
        this.uploadFirstPhotoBtn = document.getElementById('upload-first-photo-btn');
        this.shareProfileBtn = document.getElementById('share-profile-btn');
        
        this.photoModal = document.getElementById('photo-modal');
        this.modalOverlay = document.getElementById('modal-overlay');
        this.modalClose = document.getElementById('modal-close');
        this.modalImage = document.getElementById('modal-image');
        this.modalPhotoTitle = document.getElementById('modal-photo-title');
        this.modalPhotoDetails = document.getElementById('modal-photo-details');
        this.prevPhotoBtn = document.getElementById('prev-photo');
        this.nextPhotoBtn = document.getElementById('next-photo');
    }

    bindEvents() {
        // View toggle
        this.viewButtons.forEach(btn => {
            btn.addEventListener('click', (e) => this.toggleView(e.target.dataset.view));
        });
        
        // Sort change
        this.sortSelect.addEventListener('change', (e) => this.changeSortOrder(e.target.value));
        
        // Upload buttons
        this.uploadPhotoBtn.addEventListener('click', () => this.goToUpload());
        this.uploadFirstPhotoBtn.addEventListener('click', () => this.goToUpload());
        
        // Share button
        this.shareProfileBtn.addEventListener('click', () => this.shareProfile());
        
        // Modal events
        this.modalOverlay.addEventListener('click', () => this.closeModal());
        this.modalClose.addEventListener('click', () => this.closeModal());
        this.prevPhotoBtn.addEventListener('click', () => this.showPreviousPhoto());
        this.nextPhotoBtn.addEventListener('click', () => this.showNextPhoto());
        
        // Keyboard navigation
        document.addEventListener('keydown', (e) => this.handleKeydown(e));
    }

    getCatNameFromUrl() {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get('cat') || '';
    }

    async loadCatProfile() {
        if (!this.catName) {
            this.showError();
            return;
        }

        try {
            this.showLoading();
            
            const response = await fetch(`${this.apiBaseUrl}/cat/${encodeURIComponent(this.catName)}`);
            const result = await response.json();

            if (response.ok && result.success) {
                this.displayProfile(result);
                this.currentPhotos = result.photos || [];
                this.displayPhotos();
            } else {
                this.showError();
            }
        } catch (error) {
            console.error('Error loading cat profile:', error);
            this.showError();
        }
    }

    showLoading() {
        this.loadingContainer.style.display = 'flex';
        this.errorContainer.style.display = 'none';
        this.profileContainer.style.display = 'none';
    }

    showError() {
        this.loadingContainer.style.display = 'none';
        this.errorContainer.style.display = 'flex';
        this.profileContainer.style.display = 'none';
    }

    displayProfile(data) {
        this.loadingContainer.style.display = 'none';
        this.errorContainer.style.display = 'none';
        this.profileContainer.style.display = 'block';

        // Update page title
        document.title = `${data.catName} - Cat Profile - Yendor Cats`;
        
        // Display cat name
        this.catNameElement.textContent = data.catName;
        
        // Display profile image or placeholder
        if (data.photos && data.photos.length > 0) {
            this.profileImage.src = data.photos[0].url || data.photos[0].s3Url;
            this.profileImage.alt = data.catName;
            this.profileImage.style.display = 'block';
            this.profileImagePlaceholder.style.display = 'none';
        } else {
            this.profileImage.style.display = 'none';
            this.profileImagePlaceholder.style.display = 'flex';
        }
        
        // Display cat details
        this.displayCatDetails(data.catProfile);
        
        // Display stats
        this.displayStats(data);
        
        // Set upload button URL
        this.uploadPhotoBtn.href = `upload.html?cat=${encodeURIComponent(data.catName)}`;
        this.uploadFirstPhotoBtn.href = `upload.html?cat=${encodeURIComponent(data.catName)}`;
    }

    displayCatDetails(catProfile) {
        if (!catProfile) {
            this.catDetailsElement.innerHTML = `
                <div class="detail-item">
                    <div class="detail-label">Status</div>
                    <div class="detail-value">Photos only - No profile created yet</div>
                </div>
            `;
            return;
        }

        const age = this.calculateAge(catProfile.dateOfBirth);
        const genderDisplay = catProfile.gender === 'M' ? 'Male' : catProfile.gender === 'F' ? 'Female' : 'Unknown';
        
        this.catDetailsElement.innerHTML = `
            <div class="detail-item">
                <div class="detail-label">Breed</div>
                <div class="detail-value">${catProfile.breed || 'Unknown'}</div>
            </div>
            <div class="detail-item">
                <div class="detail-label">Gender</div>
                <div class="detail-value">${genderDisplay}</div>
            </div>
            <div class="detail-item">
                <div class="detail-label">Age</div>
                <div class="detail-value">${age}</div>
            </div>
            <div class="detail-item">
                <div class="detail-label">Color</div>
                <div class="detail-value">${catProfile.color || 'Unknown'}</div>
            </div>
            ${catProfile.markings ? `
                <div class="detail-item">
                    <div class="detail-label">Markings</div>
                    <div class="detail-value">${catProfile.markings}</div>
                </div>
            ` : ''}
            ${catProfile.description ? `
                <div class="detail-item">
                    <div class="detail-label">Description</div>
                    <div class="detail-value">${catProfile.description}</div>
                </div>
            ` : ''}
            <div class="detail-item">
                <div class="detail-label">Availability</div>
                <div class="detail-value">${catProfile.isAvailable ? 'Available' : 'Not Available'}</div>
            </div>
        `;
    }

    displayStats(data) {
        this.photoCountElement.textContent = data.photoCount || 0;
        
        if (data.catProfile && data.catProfile.dateOfBirth) {
            this.ageDisplayElement.textContent = this.calculateAge(data.catProfile.dateOfBirth);
        } else {
            this.ageDisplayElement.textContent = 'Unknown';
        }
        
        if (data.timeline && data.timeline.dateRange) {
            this.firstPhotoDateElement.textContent = this.formatDate(data.timeline.dateRange.earliest);
            this.latestPhotoDateElement.textContent = this.formatDate(data.timeline.dateRange.latest);
        } else {
            this.firstPhotoDateElement.textContent = '-';
            this.latestPhotoDateElement.textContent = '-';
        }
    }

    displayPhotos() {
        if (this.currentPhotos.length === 0) {
            this.photosGrid.style.display = 'none';
            this.timelineView.style.display = 'none';
            this.emptyState.style.display = 'block';
            return;
        }

        this.emptyState.style.display = 'none';
        
        if (this.currentView === 'grid') {
            this.displayGridView();
        } else {
            this.displayTimelineView();
        }
    }

    displayGridView() {
        this.photosGrid.style.display = 'grid';
        this.timelineView.style.display = 'none';
        
        const sortedPhotos = this.sortPhotos(this.currentPhotos);
        
        this.photosGrid.innerHTML = sortedPhotos.map((photo, index) => `
            <div class="photo-item" data-index="${index}">
                <img src="${photo.url || photo.s3Url}" alt="${photo.catName}" loading="lazy">
                <div class="photo-info">
                    <div class="photo-date">${this.formatDate(photo.dateTaken)}</div>
                    <div class="photo-details">
                        ${photo.age ? `Age: ${photo.age} years<br>` : ''}
                        ${photo.category ? `Category: ${photo.category}<br>` : ''}
                        ${photo.notes ? photo.notes : ''}
                    </div>
                </div>
            </div>
        `).join('');
        
        // Add click handlers
        this.photosGrid.querySelectorAll('.photo-item').forEach(item => {
            item.addEventListener('click', (e) => {
                const index = parseInt(e.currentTarget.dataset.index);
                this.openModal(index);
            });
        });
    }

    displayTimelineView() {
        this.photosGrid.style.display = 'none';
        this.timelineView.style.display = 'block';
        
        const sortedPhotos = this.sortPhotos(this.currentPhotos);
        
        this.timelineView.innerHTML = `
            <div class="timeline-line"></div>
            ${sortedPhotos.map((photo, index) => `
                <div class="timeline-item" data-index="${index}">
                    <div class="timeline-content">
                        <img src="${photo.url || photo.s3Url}" alt="${photo.catName}" style="width: 100%; height: 200px; object-fit: cover; border-radius: 8px; margin-bottom: 1rem;">
                        <div class="photo-details">
                            ${photo.age ? `<p><strong>Age:</strong> ${photo.age} years</p>` : ''}
                            ${photo.category ? `<p><strong>Category:</strong> ${photo.category}</p>` : ''}
                            ${photo.notes ? `<p><strong>Notes:</strong> ${photo.notes}</p>` : ''}
                        </div>
                    </div>
                    <div class="timeline-date">${this.formatDate(photo.dateTaken)}</div>
                </div>
            `).join('')}
        `;
        
        // Add click handlers
        this.timelineView.querySelectorAll('.timeline-item').forEach(item => {
            item.addEventListener('click', (e) => {
                const index = parseInt(e.currentTarget.dataset.index);
                this.openModal(index);
            });
        });
    }

    sortPhotos(photos) {
        const sorted = [...photos];
        
        switch (this.currentSort) {
            case 'date-asc':
                return sorted.sort((a, b) => new Date(a.dateTaken) - new Date(b.dateTaken));
            case 'date-desc':
                return sorted.sort((a, b) => new Date(b.dateTaken) - new Date(a.dateTaken));
            default:
                return sorted;
        }
    }

    toggleView(view) {
        this.currentView = view;
        
        this.viewButtons.forEach(btn => {
            btn.classList.toggle('active', btn.dataset.view === view);
        });
        
        this.displayPhotos();
    }

    changeSortOrder(sortOrder) {
        this.currentSort = sortOrder;
        this.displayPhotos();
    }

    openModal(photoIndex) {
        this.currentPhotoIndex = photoIndex;
        this.updateModal();
        this.photoModal.style.display = 'block';
        document.body.style.overflow = 'hidden';
    }

    closeModal() {
        this.photoModal.style.display = 'none';
        document.body.style.overflow = '';
    }

    updateModal() {
        const photo = this.currentPhotos[this.currentPhotoIndex];
        if (!photo) return;
        
        this.modalImage.src = photo.url || photo.s3Url;
        this.modalImage.alt = photo.catName;
        this.modalPhotoTitle.textContent = `${photo.catName} - ${this.formatDate(photo.dateTaken)}`;
        
        this.modalPhotoDetails.innerHTML = `
            ${photo.age ? `<p><strong>Age:</strong> ${photo.age} years</p>` : ''}
            ${photo.category ? `<p><strong>Category:</strong> ${photo.category}</p>` : ''}
            ${photo.breed ? `<p><strong>Breed:</strong> ${photo.breed}</p>` : ''}
            ${photo.hairColor ? `<p><strong>Hair Color:</strong> ${photo.hairColor}</p>` : ''}
            ${photo.eyeColor ? `<p><strong>Eye Color:</strong> ${photo.eyeColor}</p>` : ''}
            ${photo.markings ? `<p><strong>Markings:</strong> ${photo.markings}</p>` : ''}
            ${photo.bloodline ? `<p><strong>Bloodline:</strong> ${photo.bloodline}</p>` : ''}
            ${photo.tags ? `<p><strong>Tags:</strong> ${photo.tags}</p>` : ''}
            ${photo.notes ? `<p><strong>Notes:</strong> ${photo.notes}</p>` : ''}
            <p><strong>File Size:</strong> ${this.formatFileSize(photo.fileSize)}</p>
            <p><strong>Uploaded:</strong> ${this.formatDate(photo.dateUploaded)}</p>
        `;
    }

    showPreviousPhoto() {
        if (this.currentPhotoIndex > 0) {
            this.currentPhotoIndex--;
            this.updateModal();
        }
    }

    showNextPhoto() {
        if (this.currentPhotoIndex < this.currentPhotos.length - 1) {
            this.currentPhotoIndex++;
            this.updateModal();
        }
    }

    handleKeydown(e) {
        if (this.photoModal.style.display === 'block') {
            switch (e.key) {
                case 'Escape':
                    this.closeModal();
                    break;
                case 'ArrowLeft':
                    this.showPreviousPhoto();
                    break;
                case 'ArrowRight':
                    this.showNextPhoto();
                    break;
            }
        }
    }

    goToUpload() {
        window.location.href = `upload.html?cat=${encodeURIComponent(this.catName)}`;
    }

    shareProfile() {
        if (navigator.share) {
            navigator.share({
                title: `${this.catName} - Cat Profile`,
                text: `Check out ${this.catName}'s profile on Yendor Cats`,
                url: window.location.href
            });
        } else {
            // Fallback: copy URL to clipboard
            navigator.clipboard.writeText(window.location.href).then(() => {
                alert('Profile URL copied to clipboard!');
            });
        }
    }

    calculateAge(dateOfBirth) {
        if (!dateOfBirth) return 'Unknown';
        
        const birth = new Date(dateOfBirth);
        const now = new Date();
        const ageInYears = (now - birth) / (365.25 * 24 * 60 * 60 * 1000);
        
        if (ageInYears < 1) {
            const ageInMonths = Math.floor(ageInYears * 12);
            return `${ageInMonths} month${ageInMonths !== 1 ? 's' : ''}`;
        } else {
            return `${Math.floor(ageInYears)} year${Math.floor(ageInYears) !== 1 ? 's' : ''}`;
        }
    }

    formatDate(dateString) {
        if (!dateString) return 'Unknown';
        
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    }

    formatFileSize(bytes) {
        if (!bytes) return 'Unknown';
        
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    }
}

// Initialize cat profile when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new CatProfile();
});
