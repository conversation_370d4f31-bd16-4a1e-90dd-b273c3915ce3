/**
 * Simple live reload script that checks for changes to static files
 * and refreshes the browser automatically when a change is detected.
 */

(function() {
    // Skip this script if we're not in development mode
    if (window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1') {
        console.log('Live reload disabled in production');
        return;
    }

    console.log('Live reload initialized');
    
    // Create a version timestamp for resources
    let versionTimestamp = new Date().getTime();
    
    // Check for changes every 2 seconds
    setInterval(function() {
        // Make a request to a special endpoint that will be modified each time a file changes
        fetch('/live-reload-check.js?t=' + new Date().getTime())
            .then(response => response.text())
            .then(data => {
                // Extract the version from the file
                const match = data.match(/const\s+VERSION\s*=\s*['"]([^'"]+)['"]/);
                if (match && match[1]) {
                    const newVersion = match[1];
                    
                    // If version has changed, reload the page
                    if (newVersion !== versionTimestamp) {
                        console.log('Changes detected, reloading page...');
                        versionTimestamp = newVersion;
                        window.location.reload();
                    }
                }
            })
            .catch(error => {
                console.error('Live reload check failed:', error);
            });
    }, 2000);
})(); 