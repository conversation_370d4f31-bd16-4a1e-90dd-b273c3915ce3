/*
 * Yendor Cats - Button Styles
 * Styles for all button elements and classes
 */

.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius-md);
    font-weight: 600;
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-medium);
}

.btn-primary {
    background-color: var(--accent-primary);
    color: var(--text-navbar);
    border: 2px solid var(--accent-primary);
}

.btn-primary:hover {
    background-color: var(--accent-secondary);
    border-color: var(--accent-secondary);
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background-color: transparent;
    color: var(--accent-primary);
    border: 2px solid var(--accent-primary);
}

.btn-secondary:hover {
    background-color: var(--accent-primary);
    color: var(--text-navbar);
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.btn-large {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

.btn-small {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

/* Back to top button */
.back-to-top {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 5rem;
    height: 5rem;
    background-color: var(--accent-primary);
    color: var(--text-navbar);
    border-radius: 50%;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-medium);
    z-index: 99;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
}

.back-to-top svg {
    width: 2rem;
    height: 2rem;
    stroke-width: 3px;
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background-color: var(--accent-secondary);
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.4);
} 