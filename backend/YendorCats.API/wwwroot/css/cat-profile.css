/* Cat Profile Page Styles */

.main-content {
    padding-top: 80px;
}

/* Loading and Error States */
.loading-container, .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 60vh;
    text-align: center;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #8B4513;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error-content h2 {
    color: #495057;
    margin-bottom: 1rem;
}

.error-content p {
    color: #6c757d;
    margin-bottom: 2rem;
}

/* Profile Header */
.profile-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 2rem 0;
}

.profile-header-content {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 2rem;
    align-items: center;
}

.profile-image-container {
    position: relative;
    width: 200px;
    height: 200px;
}

.profile-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
    border: 4px solid white;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.profile-image-placeholder {
    width: 100%;
    height: 100%;
    background: #e9ecef;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 4px solid white;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.placeholder-icon {
    font-size: 4rem;
    opacity: 0.5;
}

.profile-info {
    min-width: 0;
}

.cat-name {
    font-size: 2.5rem;
    color: #495057;
    margin-bottom: 1rem;
    font-weight: 700;
}

.cat-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.detail-item {
    background: white;
    padding: 1rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.detail-label {
    font-weight: 600;
    color: #6c757d;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.5rem;
}

.detail-value {
    color: #495057;
    font-size: 1.1rem;
}

.profile-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn {
    padding: 12px 24px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 600;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: #8B4513;
    color: white;
}

.btn-primary:hover {
    background: #6d3410;
    transform: translateY(-2px);
}

.btn-secondary {
    background: white;
    color: #8B4513;
    border: 2px solid #8B4513;
}

.btn-secondary:hover {
    background: #8B4513;
    color: white;
}

/* Profile Stats */
.profile-stats {
    background: white;
    padding: 2rem 0;
    border-bottom: 1px solid #dee2e6;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 2rem;
    text-align: center;
}

.stat-item {
    padding: 1rem;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: #8B4513;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Photo Timeline */
.photo-timeline {
    padding: 3rem 0;
}

.timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.timeline-header h2 {
    color: #495057;
    margin: 0;
}

.timeline-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.sort-select {
    padding: 8px 12px;
    border: 2px solid #dee2e6;
    border-radius: 6px;
    background: white;
    color: #495057;
}

.view-toggle {
    display: flex;
    border: 2px solid #dee2e6;
    border-radius: 6px;
    overflow: hidden;
}

.view-btn {
    padding: 8px 16px;
    background: white;
    border: none;
    color: #6c757d;
    cursor: pointer;
    transition: all 0.3s ease;
}

.view-btn.active {
    background: #8B4513;
    color: white;
}

.view-btn:hover:not(.active) {
    background: #f8f9fa;
}

/* Photos Grid */
.photos-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
}

.photo-item {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    cursor: pointer;
}

.photo-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.photo-item img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.photo-info {
    padding: 1rem;
}

.photo-date {
    color: #8B4513;
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.photo-details {
    color: #6c757d;
    font-size: 0.9rem;
    line-height: 1.4;
}

/* Timeline View */
.timeline-view {
    position: relative;
}

.timeline-line {
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
    transform: translateX(-50%);
}

.timeline-item {
    display: flex;
    margin-bottom: 3rem;
    position: relative;
}

.timeline-item:nth-child(even) {
    flex-direction: row-reverse;
}

.timeline-content {
    width: calc(50% - 2rem);
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    position: relative;
}

.timeline-date {
    position: absolute;
    left: 50%;
    top: 1.5rem;
    transform: translateX(-50%);
    background: #8B4513;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    white-space: nowrap;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
}

.empty-content {
    max-width: 400px;
    margin: 0 auto;
}

.empty-icon {
    font-size: 4rem;
    opacity: 0.5;
    margin-bottom: 1rem;
    display: block;
}

.empty-content h3 {
    color: #495057;
    margin-bottom: 1rem;
}

.empty-content p {
    color: #6c757d;
    margin-bottom: 2rem;
}

/* Photo Modal */
.photo-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    display: none;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
}

.modal-content {
    position: relative;
    width: 90%;
    max-width: 1200px;
    height: 90%;
    margin: 5% auto;
    background: white;
    border-radius: 12px;
    overflow: hidden;
    display: flex;
}

.modal-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    font-size: 1.5rem;
    cursor: pointer;
    z-index: 1001;
}

.modal-image-container {
    flex: 1;
    position: relative;
    background: #000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-image-container img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.modal-nav {
    position: absolute;
    top: 50%;
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 1rem;
    transform: translateY(-50%);
}

.nav-btn {
    background: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    font-size: 1.5rem;
    cursor: pointer;
    transition: background 0.3s ease;
}

.nav-btn:hover {
    background: rgba(0, 0, 0, 0.7);
}

.modal-info {
    width: 300px;
    padding: 2rem;
    background: white;
    overflow-y: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
    .profile-header-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 1rem;
    }
    
    .profile-image-container {
        width: 150px;
        height: 150px;
        margin: 0 auto;
    }
    
    .cat-name {
        font-size: 2rem;
    }
    
    .timeline-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .timeline-controls {
        justify-content: center;
    }
    
    .photos-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 1rem;
    }
    
    .timeline-view .timeline-item {
        flex-direction: column !important;
    }
    
    .timeline-content {
        width: 100%;
        margin-left: 2rem;
    }
    
    .timeline-date {
        left: -1rem;
        transform: none;
    }
    
    .modal-content {
        flex-direction: column;
        height: 95%;
        margin: 2.5% auto;
    }
    
    .modal-info {
        width: 100%;
        max-height: 200px;
    }
}

@media (max-width: 480px) {
    .profile-image-container {
        width: 120px;
        height: 120px;
    }

    .cat-name {
        font-size: 1.8rem;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .photos-grid {
        grid-template-columns: 1fr;
    }

    .profile-actions {
        flex-direction: column;
    }

    .btn {
        text-align: center;
        justify-content: center;
    }
}
