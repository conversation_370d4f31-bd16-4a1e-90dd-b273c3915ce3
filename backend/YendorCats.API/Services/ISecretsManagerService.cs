using System.Text.Json;
using Microsoft.Extensions.Configuration;
using VaultSharp;
using VaultSharp.V1.AuthMethods.Token;
using VaultSharp.V1.Commons;

public interface ISecretsManagerService
{
    Task<AppSecrets> GetAppSecretsAsync();
    Task<string> GetSecretAsync(string secretName);
}

public class SecretsManagerService : ISecretsManagerService
{
    private readonly IVaultClient _vaultClient;
    private readonly IConfiguration _configuration;
    private readonly IWebHostEnvironment _environment;
    private readonly ILogger<SecretsManagerService> _logger;
    private AppSecrets? _cachedSecrets;

    public SecretsManagerService(
        IConfiguration configuration,
        IWebHostEnvironment environment,
        ILogger<SecretsManagerService> logger)
    {
        _configuration = configuration;
        _environment = environment;
        _logger = logger;
        
        // Initialize Vault client
        string vaultAddress = _configuration["Vault:Address"] ?? "http://localhost:8200";
        string vaultToken = _configuration["Vault:Token"] ?? Environment.GetEnvironmentVariable("VAULT_TOKEN") ?? string.Empty;
        
        var authMethod = new TokenAuthMethodInfo(vaultToken);
        var vaultClientSettings = new VaultClientSettings(vaultAddress, authMethod);
        _vaultClient = new VaultClient(vaultClientSettings);
    }

    public async Task<AppSecrets> GetAppSecretsAsync()
    {
        if (_cachedSecrets != null)
            return _cachedSecrets;

        if (_environment.IsDevelopment())
        {
            // In development, use appsettings values
            _cachedSecrets = new AppSecrets
            {
                DbConnectionString = _configuration.GetConnectionString("DefaultConnection"),
                JwtSecret = _configuration["JwtSettings:Secret"],
                JwtIssuer = _configuration["JwtSettings:Issuer"],
                JwtAudience = _configuration["JwtSettings:Audience"],
                JwtExpiryMinutes = int.Parse(_configuration["JwtSettings:ExpiryMinutes"] ?? "60"),
                RefreshExpiryDays = int.Parse(_configuration["JwtSettings:RefreshExpiryDays"] ?? "7"),

                // For development, you can set these in user secrets or environment variables
                S3AccessKey = Environment.GetEnvironmentVariable("AWS_ACCESS_KEY_ID"),
                S3SecretKey = Environment.GetEnvironmentVariable("AWS_SECRET_ACCESS_KEY"),
                S3SessionToken = Environment.GetEnvironmentVariable("AWS_SESSION_TOKEN")
            };
            return _cachedSecrets;
        }

        try
        {
            // In production, get from HashiCorp Vault
            string secretPath = _configuration["Vault:SecretPath"] ?? "secret/yendorcats/app-secrets";
            var secret = await _vaultClient.V1.Secrets.KeyValue.V2.ReadSecretAsync(secretPath);
            string secretJson = JsonSerializer.Serialize(secret.Data.Data);
            _cachedSecrets = JsonSerializer.Deserialize<AppSecrets>(secretJson) ?? new AppSecrets();
            return _cachedSecrets;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve secrets from HashiCorp Vault");
            throw;
        }
    }

    public async Task<string> GetSecretAsync(string secretName)
    {
        if (_environment.IsDevelopment() && secretName == "yendorcats/app-secrets")
        {
            // Mock secret in development to avoid costs
            return JsonSerializer.Serialize(new AppSecrets
            {
                DbConnectionString = _configuration.GetConnectionString("DefaultConnection"),
                JwtSecret = _configuration["JwtSettings:Secret"],
                JwtIssuer = _configuration["JwtSettings:Issuer"],
                JwtAudience = _configuration["JwtSettings:Audience"],
                JwtExpiryMinutes = int.Parse(_configuration["JwtSettings:ExpiryMinutes"] ?? "60"),
                RefreshExpiryDays = int.Parse(_configuration["JwtSettings:RefreshExpiryDays"] ?? "7"),

                // For development, you can set these in user secrets or environment variables
                S3AccessKey = Environment.GetEnvironmentVariable("AWS_ACCESS_KEY_ID"),
                S3SecretKey = Environment.GetEnvironmentVariable("AWS_SECRET_ACCESS_KEY"),
                S3SessionToken = Environment.GetEnvironmentVariable("AWS_SESSION_TOKEN")
            });
        }

        try
        {
            // In production, get from HashiCorp Vault
            string secretPath = secretName.StartsWith("secret/") ? secretName : $"secret/{secretName}";
            var secret = await _vaultClient.V1.Secrets.KeyValue.V2.ReadSecretAsync(secretPath);
            return JsonSerializer.Serialize(secret.Data.Data);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error retrieving secret {secretName} from HashiCorp Vault");
            throw;
        }
    }
}
