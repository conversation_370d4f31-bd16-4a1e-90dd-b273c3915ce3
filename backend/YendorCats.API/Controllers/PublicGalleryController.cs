using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using YendorCats.API.Data;
using YendorCats.API.Models;
using YendorCats.API.Services;

namespace YendorCats.API.Controllers
{
    /// <summary>
    /// Public gallery controller for viewing cat photos (no authentication required)
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class PublicGalleryController : ControllerBase
    {
        private readonly ILogger<PublicGalleryController> _logger;
        private readonly IS3StorageService _s3StorageService;
        private readonly AppDbContext _context;

        public PublicGalleryController(
            ILogger<PublicGalleryController> logger,
            IS3StorageService s3StorageService,
            AppDbContext context)
        {
            _logger = logger;
            _s3StorageService = s3StorageService;
            _context = context;
        }

        /// <summary>
        /// Get all cat photos in a category (public access)
        /// </summary>
        /// <param name="category">Category name (studs, queens, kittens, gallery)</param>
        /// <param name="sortBy">Sort by: name, date, random</param>
        /// <param name="includeMetadata">Include detailed metadata</param>
        /// <returns>List of cat photos with public URLs</returns>
        [HttpGet("category/{category}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetCategoryImages(
            string category, 
            string sortBy = "name", 
            bool includeMetadata = false)
        {
            try
            {
                _logger.LogInformation("Public request for category: {Category}", category);

                // Validate category
                var validCategories = new[] { "studs", "queens", "kittens", "gallery" };
                if (!validCategories.Contains(category.ToLower()))
                {
                    return BadRequest(new { message = "Invalid category. Valid categories: studs, queens, kittens, gallery" });
                }

                // Get images from database first (faster)
                var dbImages = await _context.CatGalleryImages
                    .Where(img => img.Category.ToLower() == category.ToLower())
                    .ToListAsync();

                if (dbImages.Any())
                {
                    _logger.LogInformation("Found {Count} images in database for category {Category}", dbImages.Count, category);
                    
                    var sortedImages = SortImages(dbImages, sortBy);
                    var publicImages = sortedImages.Select(img => CreatePublicImageResponse(img, includeMetadata)).ToList();
                    
                    return Ok(new
                    {
                        category = category,
                        count = publicImages.Count,
                        sortBy = sortBy,
                        images = publicImages,
                        source = "database",
                        retrievedAt = DateTime.UtcNow
                    });
                }

                // Fallback to S3 if no database records
                _logger.LogInformation("No database records found, scanning S3 for category: {Category}", category);
                
                var s3Files = await _s3StorageService.ListFilesAsync($"{category}/");
                var s3Images = new List<CatGalleryImage>();

                foreach (var file in s3Files)
                {
                    if (IsImageFile(file.Key))
                    {
                        var metadata = await _s3StorageService.GetObjectMetadataAsync(file.Key);
                        var image = CatGalleryImage.FromS3Metadata(metadata, file.Key, category);
                        s3Images.Add(image);
                    }
                }

                var sortedS3Images = SortImages(s3Images, sortBy);
                var publicS3Images = sortedS3Images.Select(img => CreatePublicImageResponse(img, includeMetadata)).ToList();

                return Ok(new
                {
                    category = category,
                    count = publicS3Images.Count,
                    sortBy = sortBy,
                    images = publicS3Images,
                    source = "s3",
                    retrievedAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving public gallery for category: {Category}", category);
                return StatusCode(500, new { message = "An error occurred while retrieving the gallery" });
            }
        }

        /// <summary>
        /// Get photos for a specific cat (public access)
        /// </summary>
        /// <param name="catName">Name of the cat</param>
        /// <param name="sortBy">Sort by: date, name, random</param>
        /// <param name="includeMetadata">Include detailed metadata</param>
        /// <returns>List of photos for the specified cat</returns>
        [HttpGet("cat/{catName}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetCatPhotos(
            string catName, 
            string sortBy = "date", 
            bool includeMetadata = false)
        {
            try
            {
                _logger.LogInformation("Public request for cat photos: {CatName}", catName);

                // Get from database first
                var dbImages = await _context.CatGalleryImages
                    .Where(img => img.CatName.ToLower() == catName.ToLower())
                    .ToListAsync();

                if (dbImages.Any())
                {
                    var sortedImages = SortImages(dbImages, sortBy);
                    var publicImages = sortedImages.Select(img => CreatePublicImageResponse(img, includeMetadata)).ToList();
                    
                    return Ok(new
                    {
                        catName = catName,
                        count = publicImages.Count,
                        sortBy = sortBy,
                        images = publicImages,
                        source = "database",
                        retrievedAt = DateTime.UtcNow
                    });
                }

                // Fallback to S3 search
                var allCategories = new[] { "studs", "queens", "kittens", "gallery" };
                var catImages = new List<CatGalleryImage>();

                foreach (var category in allCategories)
                {
                    try
                    {
                        var s3Files = await _s3StorageService.ListFilesAsync($"{category}/{catName}/");
                        
                        foreach (var file in s3Files)
                        {
                            if (IsImageFile(file.Key))
                            {
                                var metadata = await _s3StorageService.GetObjectMetadataAsync(file.Key);
                                var image = CatGalleryImage.FromS3Metadata(metadata, file.Key, category);
                                catImages.Add(image);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Error searching for cat {CatName} in category {Category}", catName, category);
                    }
                }

                if (!catImages.Any())
                {
                    return NotFound(new { message = $"No photos found for cat: {catName}" });
                }

                var sortedCatImages = SortImages(catImages, sortBy);
                var publicCatImages = sortedCatImages.Select(img => CreatePublicImageResponse(img, includeMetadata)).ToList();

                return Ok(new
                {
                    catName = catName,
                    count = publicCatImages.Count,
                    sortBy = sortBy,
                    images = publicCatImages,
                    source = "s3",
                    retrievedAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving photos for cat: {CatName}", catName);
                return StatusCode(500, new { message = "An error occurred while retrieving cat photos" });
            }
        }

        /// <summary>
        /// Get random featured photos (public access)
        /// </summary>
        /// <param name="count">Number of photos to return (max 20)</param>
        /// <returns>Random selection of cat photos</returns>
        [HttpGet("featured")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetFeaturedPhotos(int count = 6)
        {
            try
            {
                count = Math.Min(count, 20); // Limit to 20 photos max

                var allImages = await _context.CatGalleryImages
                    .Where(img => !string.IsNullOrEmpty(img.CatName))
                    .ToListAsync();

                if (!allImages.Any())
                {
                    // Fallback to S3 if no database records
                    allImages = await GetRandomImagesFromS3(count * 2); // Get more to have selection
                }

                var random = new Random();
                var featuredImages = allImages
                    .OrderBy(x => random.Next())
                    .Take(count)
                    .Select(img => CreatePublicImageResponse(img, true))
                    .ToList();

                return Ok(new
                {
                    count = featuredImages.Count,
                    images = featuredImages,
                    retrievedAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving featured photos");
                return StatusCode(500, new { message = "An error occurred while retrieving featured photos" });
            }
        }

        private object CreatePublicImageResponse(CatGalleryImage image, bool includeMetadata)
        {
            var basicResponse = new
            {
                id = image.Id,
                catName = image.CatName,
                imageUrl = image.ImageUrl, // This uses the public URL template
                category = image.Category,
                dateTaken = image.DateTaken,
                orderNumber = image.OrderNumber
            };

            if (!includeMetadata)
            {
                return basicResponse;
            }

            return new
            {
                id = image.Id,
                catName = image.CatName,
                imageUrl = image.ImageUrl,
                category = image.Category,
                dateTaken = image.DateTaken,
                orderNumber = image.OrderNumber,
                age = image.Age,
                description = image.Description,
                color = image.Color,
                gender = image.Gender,
                traits = image.Traits,
                breed = image.Breed,
                personality = image.Personality,
                fileFormat = image.FileFormat,
                fileSize = image.FileSize,
                width = image.Width,
                height = image.Height
            };
        }

        private List<CatGalleryImage> SortImages(List<CatGalleryImage> images, string sortBy)
        {
            return sortBy.ToLower() switch
            {
                "date" => images.OrderByDescending(img => img.DateTaken).ToList(),
                "name" => images.OrderBy(img => img.CatName).ThenBy(img => img.DateTaken).ToList(),
                "random" => images.OrderBy(x => Guid.NewGuid()).ToList(),
                _ => images.OrderBy(img => img.CatName).ThenBy(img => img.DateTaken).ToList()
            };
        }

        private bool IsImageFile(string fileName)
        {
            var imageExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif", ".webp", ".bmp" };
            var extension = Path.GetExtension(fileName).ToLower();
            return imageExtensions.Contains(extension);
        }

        private async Task<List<CatGalleryImage>> GetRandomImagesFromS3(int maxCount)
        {
            var images = new List<CatGalleryImage>();
            var categories = new[] { "studs", "queens", "kittens", "gallery" };

            foreach (var category in categories)
            {
                try
                {
                    var files = await _s3StorageService.ListFilesAsync($"{category}/");
                    var imageFiles = files.Where(f => IsImageFile(f.Key)).Take(maxCount / categories.Length);

                    foreach (var file in imageFiles)
                    {
                        var metadata = await _s3StorageService.GetObjectMetadataAsync(file.Key);
                        var image = CatGalleryImage.FromS3Metadata(metadata, file.Key, category);
                        images.Add(image);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error getting random images from category: {Category}", category);
                }
            }

            return images;
        }
    }
}
