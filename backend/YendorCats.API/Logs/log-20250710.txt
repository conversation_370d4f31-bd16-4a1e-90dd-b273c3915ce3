2025-07-10 00:49:04.879 +10:00 [INF] Using in-memory database for demo testing (no persistence)
2025-07-10 00:49:05.324 +10:00 [INF] Created new admin user: admin with role SuperAdmin
2025-07-10 00:49:05.325 +10:00 [INF] Created default admin user: admin
2025-07-10 00:49:05.419 +10:00 [INF] Starting Yendor Cats API
2025-07-10 00:51:54.943 +10:00 [INF] Using S3 credentials from environment variables.
2025-07-10 00:51:55.058 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-10 00:51:55.060 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-10 00:51:55.067 +10:00 [INF] Using in-memory database for demo testing (no persistence)
2025-07-10 00:51:55.115 +10:00 [INF] Listing files in S3 with prefix: gallery/
2025-07-10 00:51:55.371 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-10 00:51:55.371 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-10 00:51:55.372 +10:00 [INF] Using in-memory database for demo testing (no persistence)
2025-07-10 00:51:55.391 +10:00 [INF] Listing files in S3 with prefix: studs/
2025-07-10 00:51:55.873 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-10 00:51:55.873 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-10 00:51:55.873 +10:00 [INF] Using in-memory database for demo testing (no persistence)
2025-07-10 00:51:55.874 +10:00 [INF] Listing files in S3 with prefix: queens/
2025-07-10 00:51:56.373 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-10 00:51:56.374 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-10 00:51:56.374 +10:00 [INF] Using in-memory database for demo testing (no persistence)
2025-07-10 00:51:56.378 +10:00 [INF] Listing files in S3 with prefix: kittens/
2025-07-10 00:51:56.501 +10:00 [ERR] Error retrieving cat gallery images for category gallery
Amazon.S3.AmazonS3Exception: Malformed Access Key Id
 ---> Amazon.Runtime.Internal.HttpErrorResponseException: Exception of type 'Amazon.Runtime.Internal.HttpErrorResponseException' was thrown.
   at Amazon.Runtime.HttpWebRequestMessage.ProcessHttpResponseMessage(HttpResponseMessage responseMessage)
   at Amazon.Runtime.HttpWebRequestMessage.GetResponseAsync(CancellationToken cancellationToken)
   at Amazon.Runtime.Internal.HttpHandler`1.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RedirectHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.Unmarshaller.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.AmazonS3ResponseHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.ErrorHandler.InvokeAsync[T](IExecutionContext executionContext)
   --- End of inner exception stack trace ---
   at Amazon.Runtime.Internal.HttpErrorResponseExceptionHandler.HandleExceptionStream(IRequestContext requestContext, IWebResponseData httpErrorResponse, HttpErrorResponseException exception, Stream responseStream)
   at Amazon.Runtime.Internal.HttpErrorResponseExceptionHandler.HandleExceptionAsync(IExecutionContext executionContext, HttpErrorResponseException exception)
   at Amazon.Runtime.Internal.ExceptionHandler`1.HandleAsync(IExecutionContext executionContext, Exception exception)
   at Amazon.Runtime.Internal.ErrorHandler.ProcessExceptionAsync(IExecutionContext executionContext, Exception exception)
   at Amazon.Runtime.Internal.ErrorHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.Signer.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.S3Express.S3ExpressPreSigner.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.EndpointDiscoveryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.EndpointDiscoveryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CredentialsRetriever.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RetryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RetryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.AmazonS3ExceptionHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.ErrorCallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.MetricsHandler.InvokeAsync[T](IExecutionContext executionContext)
   at YendorCats.API.Services.S3StorageService.ListFilesAsync(String prefix) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/S3StorageService.cs:line 245
   at YendorCats.API.Controllers.CatGalleryController.ScanS3ForImagesAsync(String category) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Controllers/CatGalleryController.cs:line 94
   at YendorCats.API.Controllers.CatGalleryController.GetCategoryImages(String category, String orderBy, Boolean descending) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Controllers/CatGalleryController.cs:line 46
2025-07-10 00:51:56.558 +10:00 [ERR] Error retrieving cat gallery images for category studs
Amazon.S3.AmazonS3Exception: Malformed Access Key Id
 ---> Amazon.Runtime.Internal.HttpErrorResponseException: Exception of type 'Amazon.Runtime.Internal.HttpErrorResponseException' was thrown.
   at Amazon.Runtime.HttpWebRequestMessage.ProcessHttpResponseMessage(HttpResponseMessage responseMessage)
   at Amazon.Runtime.HttpWebRequestMessage.GetResponseAsync(CancellationToken cancellationToken)
   at Amazon.Runtime.Internal.HttpHandler`1.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RedirectHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.Unmarshaller.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.AmazonS3ResponseHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.ErrorHandler.InvokeAsync[T](IExecutionContext executionContext)
   --- End of inner exception stack trace ---
   at Amazon.Runtime.Internal.HttpErrorResponseExceptionHandler.HandleExceptionStream(IRequestContext requestContext, IWebResponseData httpErrorResponse, HttpErrorResponseException exception, Stream responseStream)
   at Amazon.Runtime.Internal.HttpErrorResponseExceptionHandler.HandleExceptionAsync(IExecutionContext executionContext, HttpErrorResponseException exception)
   at Amazon.Runtime.Internal.ExceptionHandler`1.HandleAsync(IExecutionContext executionContext, Exception exception)
   at Amazon.Runtime.Internal.ErrorHandler.ProcessExceptionAsync(IExecutionContext executionContext, Exception exception)
   at Amazon.Runtime.Internal.ErrorHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.Signer.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.S3Express.S3ExpressPreSigner.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.EndpointDiscoveryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.EndpointDiscoveryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CredentialsRetriever.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RetryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RetryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.AmazonS3ExceptionHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.ErrorCallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.MetricsHandler.InvokeAsync[T](IExecutionContext executionContext)
   at YendorCats.API.Services.S3StorageService.ListFilesAsync(String prefix) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/S3StorageService.cs:line 245
   at YendorCats.API.Controllers.CatGalleryController.ScanS3ForImagesAsync(String category) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Controllers/CatGalleryController.cs:line 94
   at YendorCats.API.Controllers.CatGalleryController.GetCategoryImages(String category, String orderBy, Boolean descending) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Controllers/CatGalleryController.cs:line 46
2025-07-10 00:51:56.732 +10:00 [ERR] Error retrieving cat gallery images for category queens
Amazon.S3.AmazonS3Exception: Malformed Access Key Id
 ---> Amazon.Runtime.Internal.HttpErrorResponseException: Exception of type 'Amazon.Runtime.Internal.HttpErrorResponseException' was thrown.
   at Amazon.Runtime.HttpWebRequestMessage.ProcessHttpResponseMessage(HttpResponseMessage responseMessage)
   at Amazon.Runtime.HttpWebRequestMessage.GetResponseAsync(CancellationToken cancellationToken)
   at Amazon.Runtime.Internal.HttpHandler`1.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RedirectHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.Unmarshaller.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.AmazonS3ResponseHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.ErrorHandler.InvokeAsync[T](IExecutionContext executionContext)
   --- End of inner exception stack trace ---
   at Amazon.Runtime.Internal.HttpErrorResponseExceptionHandler.HandleExceptionStream(IRequestContext requestContext, IWebResponseData httpErrorResponse, HttpErrorResponseException exception, Stream responseStream)
   at Amazon.Runtime.Internal.HttpErrorResponseExceptionHandler.HandleExceptionAsync(IExecutionContext executionContext, HttpErrorResponseException exception)
   at Amazon.Runtime.Internal.ExceptionHandler`1.HandleAsync(IExecutionContext executionContext, Exception exception)
   at Amazon.Runtime.Internal.ErrorHandler.ProcessExceptionAsync(IExecutionContext executionContext, Exception exception)
   at Amazon.Runtime.Internal.ErrorHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.Signer.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.S3Express.S3ExpressPreSigner.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.EndpointDiscoveryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.EndpointDiscoveryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CredentialsRetriever.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RetryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RetryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.AmazonS3ExceptionHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.ErrorCallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.MetricsHandler.InvokeAsync[T](IExecutionContext executionContext)
   at YendorCats.API.Services.S3StorageService.ListFilesAsync(String prefix) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/S3StorageService.cs:line 245
   at YendorCats.API.Controllers.CatGalleryController.ScanS3ForImagesAsync(String category) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Controllers/CatGalleryController.cs:line 94
   at YendorCats.API.Controllers.CatGalleryController.GetCategoryImages(String category, String orderBy, Boolean descending) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Controllers/CatGalleryController.cs:line 46
2025-07-10 00:51:56.803 +10:00 [ERR] Error retrieving cat gallery images for category kittens
Amazon.S3.AmazonS3Exception: Malformed Access Key Id
 ---> Amazon.Runtime.Internal.HttpErrorResponseException: Exception of type 'Amazon.Runtime.Internal.HttpErrorResponseException' was thrown.
   at Amazon.Runtime.HttpWebRequestMessage.ProcessHttpResponseMessage(HttpResponseMessage responseMessage)
   at Amazon.Runtime.HttpWebRequestMessage.GetResponseAsync(CancellationToken cancellationToken)
   at Amazon.Runtime.Internal.HttpHandler`1.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RedirectHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.Unmarshaller.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.AmazonS3ResponseHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.ErrorHandler.InvokeAsync[T](IExecutionContext executionContext)
   --- End of inner exception stack trace ---
   at Amazon.Runtime.Internal.HttpErrorResponseExceptionHandler.HandleExceptionStream(IRequestContext requestContext, IWebResponseData httpErrorResponse, HttpErrorResponseException exception, Stream responseStream)
   at Amazon.Runtime.Internal.HttpErrorResponseExceptionHandler.HandleExceptionAsync(IExecutionContext executionContext, HttpErrorResponseException exception)
   at Amazon.Runtime.Internal.ExceptionHandler`1.HandleAsync(IExecutionContext executionContext, Exception exception)
   at Amazon.Runtime.Internal.ErrorHandler.ProcessExceptionAsync(IExecutionContext executionContext, Exception exception)
   at Amazon.Runtime.Internal.ErrorHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.Signer.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.S3Express.S3ExpressPreSigner.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.EndpointDiscoveryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.EndpointDiscoveryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CredentialsRetriever.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RetryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RetryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.AmazonS3ExceptionHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.ErrorCallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.MetricsHandler.InvokeAsync[T](IExecutionContext executionContext)
   at YendorCats.API.Services.S3StorageService.ListFilesAsync(String prefix) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/S3StorageService.cs:line 245
   at YendorCats.API.Controllers.CatGalleryController.ScanS3ForImagesAsync(String category) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Controllers/CatGalleryController.cs:line 94
   at YendorCats.API.Controllers.CatGalleryController.GetCategoryImages(String category, String orderBy, Boolean descending) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Controllers/CatGalleryController.cs:line 46
2025-07-10 00:52:06.295 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-10 00:52:06.295 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-10 00:52:06.295 +10:00 [INF] Using in-memory database for demo testing (no persistence)
2025-07-10 00:52:06.297 +10:00 [INF] Listing files in S3 with prefix: studs/
2025-07-10 00:52:06.586 +10:00 [ERR] Error retrieving cat gallery images for category studs
Amazon.S3.AmazonS3Exception: Malformed Access Key Id
 ---> Amazon.Runtime.Internal.HttpErrorResponseException: Exception of type 'Amazon.Runtime.Internal.HttpErrorResponseException' was thrown.
   at Amazon.Runtime.HttpWebRequestMessage.ProcessHttpResponseMessage(HttpResponseMessage responseMessage)
   at Amazon.Runtime.HttpWebRequestMessage.GetResponseAsync(CancellationToken cancellationToken)
   at Amazon.Runtime.Internal.HttpHandler`1.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RedirectHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.Unmarshaller.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.AmazonS3ResponseHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.ErrorHandler.InvokeAsync[T](IExecutionContext executionContext)
   --- End of inner exception stack trace ---
   at Amazon.Runtime.Internal.HttpErrorResponseExceptionHandler.HandleExceptionStream(IRequestContext requestContext, IWebResponseData httpErrorResponse, HttpErrorResponseException exception, Stream responseStream)
   at Amazon.Runtime.Internal.HttpErrorResponseExceptionHandler.HandleExceptionAsync(IExecutionContext executionContext, HttpErrorResponseException exception)
   at Amazon.Runtime.Internal.ExceptionHandler`1.HandleAsync(IExecutionContext executionContext, Exception exception)
   at Amazon.Runtime.Internal.ErrorHandler.ProcessExceptionAsync(IExecutionContext executionContext, Exception exception)
   at Amazon.Runtime.Internal.ErrorHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.Signer.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.S3Express.S3ExpressPreSigner.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.EndpointDiscoveryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.EndpointDiscoveryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CredentialsRetriever.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RetryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RetryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.AmazonS3ExceptionHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.ErrorCallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.MetricsHandler.InvokeAsync[T](IExecutionContext executionContext)
   at YendorCats.API.Services.S3StorageService.ListFilesAsync(String prefix) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/S3StorageService.cs:line 245
   at YendorCats.API.Controllers.CatGalleryController.ScanS3ForImagesAsync(String category) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Controllers/CatGalleryController.cs:line 94
   at YendorCats.API.Controllers.CatGalleryController.GetCategoryImages(String category, String orderBy, Boolean descending) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Controllers/CatGalleryController.cs:line 46
2025-07-10 00:52:06.795 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-10 00:52:06.795 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-10 00:52:06.796 +10:00 [INF] Using in-memory database for demo testing (no persistence)
2025-07-10 00:52:06.797 +10:00 [INF] Listing files in S3 with prefix: queens/
2025-07-10 00:52:07.088 +10:00 [ERR] Error retrieving cat gallery images for category queens
Amazon.S3.AmazonS3Exception: Malformed Access Key Id
 ---> Amazon.Runtime.Internal.HttpErrorResponseException: Exception of type 'Amazon.Runtime.Internal.HttpErrorResponseException' was thrown.
   at Amazon.Runtime.HttpWebRequestMessage.ProcessHttpResponseMessage(HttpResponseMessage responseMessage)
   at Amazon.Runtime.HttpWebRequestMessage.GetResponseAsync(CancellationToken cancellationToken)
   at Amazon.Runtime.Internal.HttpHandler`1.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RedirectHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.Unmarshaller.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.AmazonS3ResponseHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.ErrorHandler.InvokeAsync[T](IExecutionContext executionContext)
   --- End of inner exception stack trace ---
   at Amazon.Runtime.Internal.HttpErrorResponseExceptionHandler.HandleExceptionStream(IRequestContext requestContext, IWebResponseData httpErrorResponse, HttpErrorResponseException exception, Stream responseStream)
   at Amazon.Runtime.Internal.HttpErrorResponseExceptionHandler.HandleExceptionAsync(IExecutionContext executionContext, HttpErrorResponseException exception)
   at Amazon.Runtime.Internal.ExceptionHandler`1.HandleAsync(IExecutionContext executionContext, Exception exception)
   at Amazon.Runtime.Internal.ErrorHandler.ProcessExceptionAsync(IExecutionContext executionContext, Exception exception)
   at Amazon.Runtime.Internal.ErrorHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.Signer.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.S3Express.S3ExpressPreSigner.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.EndpointDiscoveryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.EndpointDiscoveryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CredentialsRetriever.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RetryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RetryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.AmazonS3ExceptionHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.ErrorCallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.MetricsHandler.InvokeAsync[T](IExecutionContext executionContext)
   at YendorCats.API.Services.S3StorageService.ListFilesAsync(String prefix) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/S3StorageService.cs:line 245
   at YendorCats.API.Controllers.CatGalleryController.ScanS3ForImagesAsync(String category) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Controllers/CatGalleryController.cs:line 94
   at YendorCats.API.Controllers.CatGalleryController.GetCategoryImages(String category, String orderBy, Boolean descending) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Controllers/CatGalleryController.cs:line 46
2025-07-10 00:52:07.294 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-10 00:52:07.294 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-10 00:52:07.294 +10:00 [INF] Using in-memory database for demo testing (no persistence)
2025-07-10 00:52:07.295 +10:00 [INF] Listing files in S3 with prefix: kittens/
2025-07-10 00:52:07.574 +10:00 [ERR] Error retrieving cat gallery images for category kittens
Amazon.S3.AmazonS3Exception: Malformed Access Key Id
 ---> Amazon.Runtime.Internal.HttpErrorResponseException: Exception of type 'Amazon.Runtime.Internal.HttpErrorResponseException' was thrown.
   at Amazon.Runtime.HttpWebRequestMessage.ProcessHttpResponseMessage(HttpResponseMessage responseMessage)
   at Amazon.Runtime.HttpWebRequestMessage.GetResponseAsync(CancellationToken cancellationToken)
   at Amazon.Runtime.Internal.HttpHandler`1.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RedirectHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.Unmarshaller.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.AmazonS3ResponseHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.ErrorHandler.InvokeAsync[T](IExecutionContext executionContext)
   --- End of inner exception stack trace ---
   at Amazon.Runtime.Internal.HttpErrorResponseExceptionHandler.HandleExceptionStream(IRequestContext requestContext, IWebResponseData httpErrorResponse, HttpErrorResponseException exception, Stream responseStream)
   at Amazon.Runtime.Internal.HttpErrorResponseExceptionHandler.HandleExceptionAsync(IExecutionContext executionContext, HttpErrorResponseException exception)
   at Amazon.Runtime.Internal.ExceptionHandler`1.HandleAsync(IExecutionContext executionContext, Exception exception)
   at Amazon.Runtime.Internal.ErrorHandler.ProcessExceptionAsync(IExecutionContext executionContext, Exception exception)
   at Amazon.Runtime.Internal.ErrorHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.Signer.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.S3Express.S3ExpressPreSigner.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.EndpointDiscoveryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.EndpointDiscoveryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CredentialsRetriever.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RetryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RetryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.AmazonS3ExceptionHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.ErrorCallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.MetricsHandler.InvokeAsync[T](IExecutionContext executionContext)
   at YendorCats.API.Services.S3StorageService.ListFilesAsync(String prefix) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/S3StorageService.cs:line 245
   at YendorCats.API.Controllers.CatGalleryController.ScanS3ForImagesAsync(String category) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Controllers/CatGalleryController.cs:line 94
   at YendorCats.API.Controllers.CatGalleryController.GetCategoryImages(String category, String orderBy, Boolean descending) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Controllers/CatGalleryController.cs:line 46
2025-07-10 00:52:10.119 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-10 00:52:10.119 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-10 00:52:10.120 +10:00 [INF] Using in-memory database for demo testing (no persistence)
2025-07-10 00:52:10.121 +10:00 [INF] Listing files in S3 with prefix: gallery/
2025-07-10 00:52:10.408 +10:00 [ERR] Error retrieving cat gallery images for category gallery
Amazon.S3.AmazonS3Exception: Malformed Access Key Id
 ---> Amazon.Runtime.Internal.HttpErrorResponseException: Exception of type 'Amazon.Runtime.Internal.HttpErrorResponseException' was thrown.
   at Amazon.Runtime.HttpWebRequestMessage.ProcessHttpResponseMessage(HttpResponseMessage responseMessage)
   at Amazon.Runtime.HttpWebRequestMessage.GetResponseAsync(CancellationToken cancellationToken)
   at Amazon.Runtime.Internal.HttpHandler`1.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RedirectHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.Unmarshaller.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.AmazonS3ResponseHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.ErrorHandler.InvokeAsync[T](IExecutionContext executionContext)
   --- End of inner exception stack trace ---
   at Amazon.Runtime.Internal.HttpErrorResponseExceptionHandler.HandleExceptionStream(IRequestContext requestContext, IWebResponseData httpErrorResponse, HttpErrorResponseException exception, Stream responseStream)
   at Amazon.Runtime.Internal.HttpErrorResponseExceptionHandler.HandleExceptionAsync(IExecutionContext executionContext, HttpErrorResponseException exception)
   at Amazon.Runtime.Internal.ExceptionHandler`1.HandleAsync(IExecutionContext executionContext, Exception exception)
   at Amazon.Runtime.Internal.ErrorHandler.ProcessExceptionAsync(IExecutionContext executionContext, Exception exception)
   at Amazon.Runtime.Internal.ErrorHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.Signer.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.S3Express.S3ExpressPreSigner.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.EndpointDiscoveryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.EndpointDiscoveryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CredentialsRetriever.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RetryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RetryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.AmazonS3ExceptionHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.ErrorCallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.MetricsHandler.InvokeAsync[T](IExecutionContext executionContext)
   at YendorCats.API.Services.S3StorageService.ListFilesAsync(String prefix) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/S3StorageService.cs:line 245
   at YendorCats.API.Controllers.CatGalleryController.ScanS3ForImagesAsync(String category) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Controllers/CatGalleryController.cs:line 94
   at YendorCats.API.Controllers.CatGalleryController.GetCategoryImages(String category, String orderBy, Boolean descending) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Controllers/CatGalleryController.cs:line 46
2025-07-10 00:52:10.604 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-10 00:52:10.605 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-10 00:52:10.605 +10:00 [INF] Using in-memory database for demo testing (no persistence)
2025-07-10 00:52:10.606 +10:00 [INF] Listing files in S3 with prefix: studs/
2025-07-10 00:52:10.895 +10:00 [ERR] Error retrieving cat gallery images for category studs
Amazon.S3.AmazonS3Exception: Malformed Access Key Id
 ---> Amazon.Runtime.Internal.HttpErrorResponseException: Exception of type 'Amazon.Runtime.Internal.HttpErrorResponseException' was thrown.
   at Amazon.Runtime.HttpWebRequestMessage.ProcessHttpResponseMessage(HttpResponseMessage responseMessage)
   at Amazon.Runtime.HttpWebRequestMessage.GetResponseAsync(CancellationToken cancellationToken)
   at Amazon.Runtime.Internal.HttpHandler`1.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RedirectHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.Unmarshaller.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.AmazonS3ResponseHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.ErrorHandler.InvokeAsync[T](IExecutionContext executionContext)
   --- End of inner exception stack trace ---
   at Amazon.Runtime.Internal.HttpErrorResponseExceptionHandler.HandleExceptionStream(IRequestContext requestContext, IWebResponseData httpErrorResponse, HttpErrorResponseException exception, Stream responseStream)
   at Amazon.Runtime.Internal.HttpErrorResponseExceptionHandler.HandleExceptionAsync(IExecutionContext executionContext, HttpErrorResponseException exception)
   at Amazon.Runtime.Internal.ExceptionHandler`1.HandleAsync(IExecutionContext executionContext, Exception exception)
   at Amazon.Runtime.Internal.ErrorHandler.ProcessExceptionAsync(IExecutionContext executionContext, Exception exception)
   at Amazon.Runtime.Internal.ErrorHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.Signer.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.S3Express.S3ExpressPreSigner.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.EndpointDiscoveryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.EndpointDiscoveryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CredentialsRetriever.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RetryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RetryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.AmazonS3ExceptionHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.ErrorCallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.MetricsHandler.InvokeAsync[T](IExecutionContext executionContext)
   at YendorCats.API.Services.S3StorageService.ListFilesAsync(String prefix) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/S3StorageService.cs:line 245
   at YendorCats.API.Controllers.CatGalleryController.ScanS3ForImagesAsync(String category) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Controllers/CatGalleryController.cs:line 94
   at YendorCats.API.Controllers.CatGalleryController.GetCategoryImages(String category, String orderBy, Boolean descending) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Controllers/CatGalleryController.cs:line 46
2025-07-10 00:52:11.107 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-10 00:52:11.107 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-10 00:52:11.108 +10:00 [INF] Using in-memory database for demo testing (no persistence)
2025-07-10 00:52:11.109 +10:00 [INF] Listing files in S3 with prefix: queens/
2025-07-10 00:52:11.374 +10:00 [ERR] Error retrieving cat gallery images for category queens
Amazon.S3.AmazonS3Exception: Malformed Access Key Id
 ---> Amazon.Runtime.Internal.HttpErrorResponseException: Exception of type 'Amazon.Runtime.Internal.HttpErrorResponseException' was thrown.
   at Amazon.Runtime.HttpWebRequestMessage.ProcessHttpResponseMessage(HttpResponseMessage responseMessage)
   at Amazon.Runtime.HttpWebRequestMessage.GetResponseAsync(CancellationToken cancellationToken)
   at Amazon.Runtime.Internal.HttpHandler`1.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RedirectHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.Unmarshaller.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.AmazonS3ResponseHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.ErrorHandler.InvokeAsync[T](IExecutionContext executionContext)
   --- End of inner exception stack trace ---
   at Amazon.Runtime.Internal.HttpErrorResponseExceptionHandler.HandleExceptionStream(IRequestContext requestContext, IWebResponseData httpErrorResponse, HttpErrorResponseException exception, Stream responseStream)
   at Amazon.Runtime.Internal.HttpErrorResponseExceptionHandler.HandleExceptionAsync(IExecutionContext executionContext, HttpErrorResponseException exception)
   at Amazon.Runtime.Internal.ExceptionHandler`1.HandleAsync(IExecutionContext executionContext, Exception exception)
   at Amazon.Runtime.Internal.ErrorHandler.ProcessExceptionAsync(IExecutionContext executionContext, Exception exception)
   at Amazon.Runtime.Internal.ErrorHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.Signer.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.S3Express.S3ExpressPreSigner.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.EndpointDiscoveryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.EndpointDiscoveryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CredentialsRetriever.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RetryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RetryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.AmazonS3ExceptionHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.ErrorCallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.MetricsHandler.InvokeAsync[T](IExecutionContext executionContext)
   at YendorCats.API.Services.S3StorageService.ListFilesAsync(String prefix) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/S3StorageService.cs:line 245
   at YendorCats.API.Controllers.CatGalleryController.ScanS3ForImagesAsync(String category) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Controllers/CatGalleryController.cs:line 94
   at YendorCats.API.Controllers.CatGalleryController.GetCategoryImages(String category, String orderBy, Boolean descending) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Controllers/CatGalleryController.cs:line 46
2025-07-10 00:52:11.604 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-10 00:52:11.604 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-10 00:52:11.604 +10:00 [INF] Using in-memory database for demo testing (no persistence)
2025-07-10 00:52:11.605 +10:00 [INF] Listing files in S3 with prefix: kittens/
2025-07-10 00:52:11.904 +10:00 [ERR] Error retrieving cat gallery images for category kittens
Amazon.S3.AmazonS3Exception: Malformed Access Key Id
 ---> Amazon.Runtime.Internal.HttpErrorResponseException: Exception of type 'Amazon.Runtime.Internal.HttpErrorResponseException' was thrown.
   at Amazon.Runtime.HttpWebRequestMessage.ProcessHttpResponseMessage(HttpResponseMessage responseMessage)
   at Amazon.Runtime.HttpWebRequestMessage.GetResponseAsync(CancellationToken cancellationToken)
   at Amazon.Runtime.Internal.HttpHandler`1.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RedirectHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.Unmarshaller.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.AmazonS3ResponseHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.ErrorHandler.InvokeAsync[T](IExecutionContext executionContext)
   --- End of inner exception stack trace ---
   at Amazon.Runtime.Internal.HttpErrorResponseExceptionHandler.HandleExceptionStream(IRequestContext requestContext, IWebResponseData httpErrorResponse, HttpErrorResponseException exception, Stream responseStream)
   at Amazon.Runtime.Internal.HttpErrorResponseExceptionHandler.HandleExceptionAsync(IExecutionContext executionContext, HttpErrorResponseException exception)
   at Amazon.Runtime.Internal.ExceptionHandler`1.HandleAsync(IExecutionContext executionContext, Exception exception)
   at Amazon.Runtime.Internal.ErrorHandler.ProcessExceptionAsync(IExecutionContext executionContext, Exception exception)
   at Amazon.Runtime.Internal.ErrorHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.Signer.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.S3Express.S3ExpressPreSigner.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.EndpointDiscoveryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.EndpointDiscoveryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CredentialsRetriever.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RetryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RetryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.AmazonS3ExceptionHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.ErrorCallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.MetricsHandler.InvokeAsync[T](IExecutionContext executionContext)
   at YendorCats.API.Services.S3StorageService.ListFilesAsync(String prefix) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/S3StorageService.cs:line 245
   at YendorCats.API.Controllers.CatGalleryController.ScanS3ForImagesAsync(String category) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Controllers/CatGalleryController.cs:line 94
   at YendorCats.API.Controllers.CatGalleryController.GetCategoryImages(String category, String orderBy, Boolean descending) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Controllers/CatGalleryController.cs:line 46
2025-07-10 00:58:56.120 +10:00 [ERR] Error copying file /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/frontend/js/gallery.js
System.IO.DirectoryNotFoundException: Could not find a part of the path '/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/wwwroot/js/gallery.js'.
   at System.IO.FileSystem.TryCloneFile(String sourceFullPath, String destFullPath, Boolean overwrite, Boolean& cloned)
   at System.IO.FileSystem.CopyFile(String sourceFullPath, String destFullPath, Boolean overwrite)
   at YendorCats.API.Middleware.FileWatcherMiddleware.CopyFileToWwwroot(String sourcePath, String frontendPath, String wwwrootPath, ILogger logger) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/FileWatcherMiddleware.cs:line 108
2025-07-10 00:58:57.080 +10:00 [ERR] Error copying file /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/frontend/js/gallery.js
System.IO.IOException: The process cannot access the file '/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/Yendo2025-07-10 00:58:57.096 +10:00 [ERR] Hosting failed to start
System.IO.IOException: Failed to bind to address http://127.0.0.1:5002: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: Address already in use
 ---> System.Net.Sockets.SocketException (48): Address already in use
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
2025-07-10 00:58:57.101 +10:00 [FTL] Application start-up failed
System.IO.IOException: Failed to bind to address http://127.0.0.1:5002: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: Address already in use
 ---> System.Net.Sockets.SocketException (48): Address already in use
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
   at Microsoft.Extensions.Hosting.Internal.Host.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.Run(IHost host)
   at Program.<Main>$(String[] args) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 378
2025-07-10 00:59:17.416 +10:00 [INF] Using in-memory database for demo testing (no persistence)
2025-07-10 00:59:17.832 +10:00 [INF] Created new admin user: admin with role SuperAdmin
2025-07-10 00:59:17.833 +10:00 [INF] Created default admin user: admin
2025-07-10 00:59:17.924 +10:00 [INF] Starting Yendor Cats API
2025-07-10 00:59:37.933 +10:00 [WRN] Using S3 credentials from appsettings - consider using environment variables for security.
2025-07-10 00:59:37.955 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-10 00:59:37.955 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-10 00:59:37.956 +10:00 [INF] Using in-memory database for demo testing (no persistence)
2025-07-10 00:59:37.963 +10:00 [INF] Public request for category: studs
2025-07-10 00:59:37.980 +10:00 [INF] No database records found, scanning S3 for category: studs
2025-07-10 00:59:37.981 +10:00 [INF] Listing files in S3 with prefix: studs/
2025-07-10 00:59:39.258 +10:00 [INF] Getting metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg
2025-07-10 00:59:39.541 +10:00 [INF] Retrieved metadata for file: studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg, MetadataCount: 1
2025-07-10 00:59:39.543 +10:00 [INF] Getting metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg
2025-07-10 00:59:39.828 +10:00 [INF] Retrieved metadata for file: studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg, MetadataCount: 1
2025-07-10 00:59:39.828 +10:00 [INF] Getting metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg
2025-07-10 00:59:40.113 +10:00 [INF] Retrieved metadata for file: studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg, MetadataCount: 1
2025-07-10 00:59:40.114 +10:00 [INF] Getting metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg
2025-07-10 00:59:40.409 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg, MetadataCount: 1
2025-07-10 00:59:40.410 +10:00 [INF] Getting metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg
2025-07-10 00:59:40.706 +10:00 [INF] Retrieved metadata for file: studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg, MetadataCount: 1
2025-07-10 00:59:40.707 +10:00 [INF] Getting metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg
2025-07-10 00:59:40.956 +10:00 [INF] Retrieved metadata for file: studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg, MetadataCount: 1
2025-07-10 00:59:40.957 +10:00 [INF] Getting metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg
2025-07-10 00:59:41.228 +10:00 [INF] Retrieved metadata for file: studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg, MetadataCount: 1
2025-07-10 00:59:41.229 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg
2025-07-10 00:59:41.516 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg, MetadataCount: 1
2025-07-10 00:59:41.516 +10:00 [INF] Getting metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg
2025-07-10 00:59:41.811 +10:00 [INF] Retrieved metadata for file: studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg, MetadataCount: 1
2025-07-10 00:59:41.812 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg
2025-07-10 00:59:42.109 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg, MetadataCount: 1
2025-07-10 00:59:42.109 +10:00 [INF] Getting metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg
2025-07-10 00:59:42.402 +10:00 [INF] Retrieved metadata for file: studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg, MetadataCount: 1
2025-07-10 00:59:42.402 +10:00 [INF] Getting metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg
2025-07-10 00:59:42.649 +10:00 [INF] Retrieved metadata for file: studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg, MetadataCount: 1
2025-07-10 00:59:42.650 +10:00 [INF] Getting metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg
2025-07-10 00:59:42.923 +10:00 [INF] Retrieved metadata for file: studs/louie/510586750_24370060715931660_903145896275112471_n.jpg, MetadataCount: 1
2025-07-10 00:59:42.924 +10:00 [INF] Getting metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg
2025-07-10 00:59:43.212 +10:00 [INF] Retrieved metadata for file: studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg, MetadataCount: 1
2025-07-10 00:59:43.212 +10:00 [INF] Getting metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg
2025-07-10 00:59:43.506 +10:00 [INF] Retrieved metadata for file: studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg, MetadataCount: 1
2025-07-10 00:59:43.507 +10:00 [INF] Getting metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg
2025-07-10 00:59:43.790 +10:00 [INF] Retrieved metadata for file: studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg, MetadataCount: 1
2025-07-10 00:59:43.791 +10:00 [INF] Getting metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg
2025-07-10 00:59:44.074 +10:00 [INF] Retrieved metadata for file: studs/louie/511277439_24370061375931594_530508242522424627_n.jpg, MetadataCount: 1
2025-07-10 00:59:44.075 +10:00 [INF] Getting metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg
2025-07-10 00:59:44.367 +10:00 [INF] Retrieved metadata for file: studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg, MetadataCount: 1
2025-07-10 00:59:44.368 +10:00 [INF] Getting metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg
2025-07-10 00:59:44.663 +10:00 [INF] Retrieved metadata for file: studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg, MetadataCount: 1
2025-07-10 00:59:54.847 +10:00 [INF] Using in-memory database for demo testing (no persistence)
2025-07-10 01:05:30.741 +10:00 [INF] Using in-memory database for demo testing (no persistence)
2025-07-10 01:05:31.121 +10:00 [INF] Created new admin user: admin with role SuperAdmin
2025-07-10 01:05:31.121 +10:00 [INF] Created default admin user: admin
2025-07-10 01:05:31.193 +10:00 [INF] Starting Yendor Cats API
2025-07-10 01:05:36.677 +10:00 [INF] Using S3 credentials from environment variables.
2025-07-10 01:05:36.704 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-10 01:05:36.705 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-10 01:05:36.706 +10:00 [INF] Using in-memory database for demo testing (no persistence)
2025-07-10 01:05:36.712 +10:00 [INF] Public request for category: gallery
2025-07-10 01:05:36.741 +10:00 [INF] No database records found, scanning S3 for category: gallery
2025-07-10 01:05:36.743 +10:00 [INF] Listing files in S3 with prefix: gallery/
2025-07-10 01:05:37.135 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-10 01:05:37.135 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-10 01:05:37.137 +10:00 [INF] Using in-memory database for demo testing (no persistence)
2025-07-10 01:05:37.139 +10:00 [INF] Public request for category: studs
2025-07-10 01:05:37.153 +10:00 [INF] No database records found, scanning S3 for category: studs
2025-07-10 01:05:37.153 +10:00 [INF] Listing files in S3 with prefix: studs/
2025-07-10 01:05:37.631 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-10 01:05:37.632 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-10 01:05:37.632 +10:00 [INF] Using in-memory database for demo testing (no persistence)
2025-07-10 01:05:37.633 +10:00 [INF] Public request for category: queens
2025-07-10 01:05:37.634 +10:00 [INF] No database records found, scanning S3 for category: queens
2025-07-10 01:05:37.635 +10:00 [INF] Listing files in S3 with prefix: queens/
2025-07-10 01:05:38.051 +10:00 [ERR] Error retrieving public gallery for category: gallery
Amazon.S3.AmazonS3Exception: Malformed Access Key Id
 ---> Amazon.Runtime.Internal.HttpErrorResponseException: Exception of type 'Amazon.Runtime.Internal.HttpErrorResponseException' was thrown.
   at Amazon.Runtime.HttpWebRequestMessage.ProcessHttpResponseMessage(HttpResponseMessage responseMessage)
   at Amazon.Runtime.HttpWebRequestMessage.GetResponseAsync(CancellationToken cancellationToken)
   at Amazon.Runtime.Internal.HttpHandler`1.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RedirectHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.Unmarshaller.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.AmazonS3ResponseHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.ErrorHandler.InvokeAsync[T](IExecutionContext executionContext)
   --- End of inner exception stack trace ---
   at Amazon.Runtime.Internal.HttpErrorResponseExceptionHandler.HandleExceptionStream(IRequestContext requestContext, IWebResponseData httpErrorResponse, HttpErrorResponseException exception, Stream responseStream)
   at Amazon.Runtime.Internal.HttpErrorResponseExceptionHandler.HandleExceptionAsync(IExecutionContext executionContext, HttpErrorResponseException exception)
   at Amazon.Runtime.Internal.ExceptionHandler`1.HandleAsync(IExecutionContext executionContext, Exception exception)
   at Amazon.Runtime.Internal.ErrorHandler.ProcessExceptionAsync(IExecutionContext executionContext, Exception exception)
   at Amazon.Runtime.Internal.ErrorHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.Signer.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.S3Express.S3ExpressPreSigner.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.EndpointDiscoveryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.EndpointDiscoveryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CredentialsRetriever.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RetryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RetryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.AmazonS3ExceptionHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.ErrorCallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.MetricsHandler.InvokeAsync[T](IExecutionContext executionContext)
   at YendorCats.API.Services.S3StorageService.ListFilesAsync(String prefix) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/S3StorageService.cs:line 245
   at YendorCats.API.Controllers.PublicGalleryController.GetCategoryImages(String category, String sortBy, Boolean includeMetadata) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Controllers/PublicGalleryController.cs:line 83
2025-07-10 01:05:38.129 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-10 01:05:38.129 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-10 01:05:38.129 +10:00 [INF] Using in-memory database for demo testing (no persistence)
2025-07-10 01:05:38.129 +10:00 [INF] Public request for category: kittens
2025-07-10 01:05:38.130 +10:00 [INF] No database records found, scanning S3 for category: kittens
2025-07-10 01:05:38.130 +10:00 [INF] Listing files in S3 with prefix: kittens/
2025-07-10 01:05:38.277 +10:00 [ERR] Error retrieving public gallery for category: studs
Amazon.S3.AmazonS3Exception: Malformed Access Key Id
 ---> Amazon.Runtime.Internal.HttpErrorResponseException: Exception of type 'Amazon.Runtime.Internal.HttpErrorResponseException' was thrown.
   at Amazon.Runtime.HttpWebRequestMessage.ProcessHttpResponseMessage(HttpResponseMessage responseMessage)
   at Amazon.Runtime.HttpWebRequestMessage.GetResponseAsync(CancellationToken cancellationToken)
   at Amazon.Runtime.Internal.HttpHandler`1.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RedirectHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.Unmarshaller.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.AmazonS3ResponseHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.ErrorHandler.InvokeAsync[T](IExecutionContext executionContext)
   --- End of inner exception stack trace ---
   at Amazon.Runtime.Internal.HttpErrorResponseExceptionHandler.HandleExceptionStream(IRequestContext requestContext, IWebResponseData httpErrorResponse, HttpErrorResponseException exception, Stream responseStream)
   at Amazon.Runtime.Internal.HttpErrorResponseExceptionHandler.HandleExceptionAsync(IExecutionContext executionContext, HttpErrorResponseException exception)
   at Amazon.Runtime.Internal.ExceptionHandler`1.HandleAsync(IExecutionContext executionContext, Exception exception)
   at Amazon.Runtime.Internal.ErrorHandler.ProcessExceptionAsync(IExecutionContext executionContext, Exception exception)
   at Amazon.Runtime.Internal.ErrorHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.Signer.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.S3Express.S3ExpressPreSigner.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.EndpointDiscoveryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.EndpointDiscoveryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CredentialsRetriever.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RetryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RetryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.AmazonS3ExceptionHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.ErrorCallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.MetricsHandler.InvokeAsync[T](IExecutionContext executionContext)
   at YendorCats.API.Services.S3StorageService.ListFilesAsync(String prefix) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/S3StorageService.cs:line 245
   at YendorCats.API.Controllers.PublicGalleryController.GetCategoryImages(String category, String sortBy, Boolean includeMetadata) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Controllers/PublicGalleryController.cs:line 83
2025-07-10 01:05:38.287 +10:00 [ERR] Error retrieving public gallery for category: queens
Amazon.S3.AmazonS3Exception: Malformed Access Key Id
 ---> Amazon.Runtime.Internal.HttpErrorResponseException: Exception of type 'Amazon.Runtime.Internal.HttpErrorResponseException' was thrown.
   at Amazon.Runtime.HttpWebRequestMessage.ProcessHttpResponseMessage(HttpResponseMessage responseMessage)
   at Amazon.Runtime.HttpWebRequestMessage.GetResponseAsync(CancellationToken cancellationToken)
   at Amazon.Runtime.Internal.HttpHandler`1.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RedirectHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.Unmarshaller.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.AmazonS3ResponseHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.ErrorHandler.InvokeAsync[T](IExecutionContext executionContext)
   --- End of inner exception stack trace ---
   at Amazon.Runtime.Internal.HttpErrorResponseExceptionHandler.HandleExceptionStream(IRequestContext requestContext, IWebResponseData httpErrorResponse, HttpErrorResponseException exception, Stream responseStream)
   at Amazon.Runtime.Internal.HttpErrorResponseExceptionHandler.HandleExceptionAsync(IExecutionContext executionContext, HttpErrorResponseException exception)
   at Amazon.Runtime.Internal.ExceptionHandler`1.HandleAsync(IExecutionContext executionContext, Exception exception)
   at Amazon.Runtime.Internal.ErrorHandler.ProcessExceptionAsync(IExecutionContext executionContext, Exception exception)
   at Amazon.Runtime.Internal.ErrorHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.Signer.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.S3Express.S3ExpressPreSigner.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.EndpointDiscoveryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.EndpointDiscoveryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CredentialsRetriever.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RetryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RetryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.AmazonS3ExceptionHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.ErrorCallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.MetricsHandler.InvokeAsync[T](IExecutionContext executionContext)
   at YendorCats.API.Services.S3StorageService.ListFilesAsync(String prefix) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/S3StorageService.cs:line 245
   at YendorCats.API.Controllers.PublicGalleryController.GetCategoryImages(String category, String sortBy, Boolean includeMetadata) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Controllers/PublicGalleryController.cs:line 83
2025-07-10 01:05:38.568 +10:00 [ERR] Error retrieving public gallery for category: kittens
Amazon.S3.AmazonS3Exception: Malformed Access Key Id
 ---> Amazon.Runtime.Internal.HttpErrorResponseException: Exception of type 'Amazon.Runtime.Internal.HttpErrorResponseException' was thrown.
   at Amazon.Runtime.HttpWebRequestMessage.ProcessHttpResponseMessage(HttpResponseMessage responseMessage)
   at Amazon.Runtime.HttpWebRequestMessage.GetResponseAsync(CancellationToken cancellationToken)
   at Amazon.Runtime.Internal.HttpHandler`1.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RedirectHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.Unmarshaller.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.AmazonS3ResponseHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.ErrorHandler.InvokeAsync[T](IExecutionContext executionContext)
   --- End of inner exception stack trace ---
   at Amazon.Runtime.Internal.HttpErrorResponseExceptionHandler.HandleExceptionStream(IRequestContext requestContext, IWebResponseData httpErrorResponse, HttpErrorResponseException exception, Stream responseStream)
   at Amazon.Runtime.Internal.HttpErrorResponseExceptionHandler.HandleExceptionAsync(IExecutionContext executionContext, HttpErrorResponseException exception)
   at Amazon.Runtime.Internal.ExceptionHandler`1.HandleAsync(IExecutionContext executionContext, Exception exception)
   at Amazon.Runtime.Internal.ErrorHandler.ProcessExceptionAsync(IExecutionContext executionContext, Exception exception)
   at Amazon.Runtime.Internal.ErrorHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.Signer.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.S3Express.S3ExpressPreSigner.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.EndpointDiscoveryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.EndpointDiscoveryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CredentialsRetriever.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RetryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RetryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.AmazonS3ExceptionHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.ErrorCallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.MetricsHandler.InvokeAsync[T](IExecutionContext executionContext)
   at YendorCats.API.Services.S3StorageService.ListFilesAsync(String prefix) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/S3StorageService.cs:line 245
   at YendorCats.API.Controllers.PublicGalleryController.GetCategoryImages(String category, String sortBy, Boolean includeMetadata) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Controllers/PublicGalleryController.cs:line 83
2025-07-10 01:08:26.579 +10:00 [INF] Using in-memory database for demo testing (no persistence)
2025-07-10 01:12:09.647 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-10 01:12:09.648 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-10 01:12:09.648 +10:00 [INF] Using in-memory database for demo testing (no persistence)
2025-07-10 01:12:09.657 +10:00 [INF] Listing files in S3 with prefix: gallery/
2025-07-10 01:12:10.123 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-10 01:12:10.123 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-10 01:12:10.124 +10:00 [INF] Using in-memory database for demo testing (no persistence)
2025-07-10 01:12:10.125 +10:00 [INF] Listing files in S3 with prefix: studs/
2025-07-10 01:12:10.623 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-10 01:12:10.624 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-10 01:12:10.624 +10:00 [INF] Using in-memory database for demo testing (no persistence)
2025-07-10 01:12:10.625 +10:00 [INF] Listing files in S3 with prefix: queens/
2025-07-10 01:12:10.827 +10:00 [ERR] Error retrieving cat gallery images for category gallery
Amazon.S3.AmazonS3Exception: Malformed Access Key Id
 ---> Amazon.Runtime.Internal.HttpErrorResponseException: Exception of type 'Amazon.Runtime.Internal.HttpErrorResponseException' was thrown.
   at Amazon.Runtime.HttpWebRequestMessage.ProcessHttpResponseMessage(HttpResponseMessage responseMessage)
   at Amazon.Runtime.HttpWebRequestMessage.GetResponseAsync(CancellationToken cancellationToken)
   at Amazon.Runtime.Internal.HttpHandler`1.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RedirectHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.Unmarshaller.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.AmazonS3ResponseHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.ErrorHandler.InvokeAsync[T](IExecutionContext executionContext)
   --- End of inner exception stack trace ---
   at Amazon.Runtime.Internal.HttpErrorResponseExceptionHandler.HandleExceptionStream(IRequestContext requestContext, IWebResponseData httpErrorResponse, HttpErrorResponseException exception, Stream responseStream)
   at Amazon.Runtime.Internal.HttpErrorResponseExceptionHandler.HandleExceptionAsync(IExecutionContext executionContext, HttpErrorResponseException exception)
   at Amazon.Runtime.Internal.ExceptionHandler`1.HandleAsync(IExecutionContext executionContext, Exception exception)
   at Amazon.Runtime.Internal.ErrorHandler.ProcessExceptionAsync(IExecutionContext executionContext, Exception exception)
   at Amazon.Runtime.Internal.ErrorHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.Signer.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.S3Express.S3ExpressPreSigner.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.EndpointDiscoveryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.EndpointDiscoveryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CredentialsRetriever.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RetryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RetryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.AmazonS3ExceptionHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.ErrorCallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.MetricsHandler.InvokeAsync[T](IExecutionContext executionContext)
   at YendorCats.API.Services.S3StorageService.ListFilesAsync(String prefix) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/S3StorageService.cs:line 245
   at YendorCats.API.Controllers.CatGalleryController.ScanS3ForImagesAsync(String category) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Controllers/CatGalleryController.cs:line 94
   at YendorCats.API.Controllers.CatGalleryController.GetCategoryImages(String category, String orderBy, Boolean descending) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Controllers/CatGalleryController.cs:line 46
2025-07-10 01:12:11.064 +10:00 [ERR] Error retrieving cat gallery images for category studs
Amazon.S3.AmazonS3Exception: Malformed Access Key Id
 ---> Amazon.Runtime.Internal.HttpErrorResponseException: Exception of type 'Amazon.Runtime.Internal.HttpErrorResponseException' was thrown.
   at Amazon.Runtime.HttpWebRequestMessage.ProcessHttpResponseMessage(HttpResponseMessage responseMessage)
   at Amazon.Runtime.HttpWebRequestMessage.GetResponseAsync(CancellationToken cancellationToken)
   at Amazon.Runtime.Internal.HttpHandler`1.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RedirectHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.Unmarshaller.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.AmazonS3ResponseHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.ErrorHandler.InvokeAsync[T](IExecutionContext executionContext)
   --- End of inner exception stack trace ---
   at Amazon.Runtime.Internal.HttpErrorResponseExceptionHandler.HandleExceptionStream(IRequestContext requestContext, IWebResponseData httpErrorResponse, HttpErrorResponseException exception, Stream responseStream)
   at Amazon.Runtime.Internal.HttpErrorResponseExceptionHandler.HandleExceptionAsync(IExecutionContext executionContext, HttpErrorResponseException exception)
   at Amazon.Runtime.Internal.ExceptionHandler`1.HandleAsync(IExecutionContext executionContext, Exception exception)
   at Amazon.Runtime.Internal.ErrorHandler.ProcessExceptionAsync(IExecutionContext executionContext, Exception exception)
   at Amazon.Runtime.Internal.ErrorHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.Signer.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.S3Express.S3ExpressPreSigner.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.EndpointDiscoveryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.EndpointDiscoveryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CredentialsRetriever.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RetryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RetryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.AmazonS3ExceptionHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.ErrorCallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.MetricsHandler.InvokeAsync[T](IExecutionContext executionContext)
   at YendorCats.API.Services.S3StorageService.ListFilesAsync(String prefix) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/S3StorageService.cs:line 245
   at YendorCats.API.Controllers.CatGalleryController.ScanS3ForImagesAsync(String category) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Controllers/CatGalleryController.cs:line 94
   at YendorCats.API.Controllers.CatGalleryController.GetCategoryImages(String category, String orderBy, Boolean descending) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Controllers/CatGalleryController.cs:line 46
2025-07-10 01:12:11.216 +10:00 [ERR] Error retrieving cat gallery images for category queens
Amazon.S3.AmazonS3Exception: Malformed Access Key Id
 ---> Amazon.Runtime.Internal.HttpErrorResponseException: Exception of type 'Amazon.Runtime.Internal.HttpErrorResponseException' was thrown.
   at Amazon.Runtime.HttpWebRequestMessage.ProcessHttpResponseMessage(HttpResponseMessage responseMessage)
   at Amazon.Runtime.HttpWebRequestMessage.GetResponseAsync(CancellationToken cancellationToken)
   at Amazon.Runtime.Internal.HttpHandler`1.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RedirectHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.Unmarshaller.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.AmazonS3ResponseHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.ErrorHandler.InvokeAsync[T](IExecutionContext executionContext)
   --- End of inner exception stack trace ---
   at Amazon.Runtime.Internal.HttpErrorResponseExceptionHandler.HandleExceptionStream(IRequestContext requestContext, IWebResponseData httpErrorResponse, HttpErrorResponseException exception, Stream responseStream)
   at Amazon.Runtime.Internal.HttpErrorResponseExceptionHandler.HandleExceptionAsync(IExecutionContext executionContext, HttpErrorResponseException exception)
   at Amazon.Runtime.Internal.ExceptionHandler`1.HandleAsync(IExecutionContext executionContext, Exception exception)
   at Amazon.Runtime.Internal.ErrorHandler.ProcessExceptionAsync(IExecutionContext executionContext, Exception exception)
   at Amazon.Runtime.Internal.ErrorHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.Signer.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.S3Express.S3ExpressPreSigner.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.EndpointDiscoveryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.EndpointDiscoveryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CredentialsRetriever.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RetryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RetryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.AmazonS3ExceptionHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.ErrorCallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.MetricsHandler.InvokeAsync[T](IExecutionContext executionContext)
   at YendorCats.API.Services.S3StorageService.ListFilesAsync(String prefix) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/S3StorageService.cs:line 245
   at YendorCats.API.Controllers.CatGalleryController.ScanS3ForImagesAsync(String category) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Controllers/CatGalleryController.cs:line 94
   at YendorCats.API.Controllers.CatGalleryController.GetCategoryImages(String category, String orderBy, Boolean descending) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Controllers/CatGalleryController.cs:line 46
2025-07-10 01:12:11.803 +10:00 [INF] Using S3 client with endpoint: https://s3.us-west-004.backblazeb2.com
2025-07-10 01:12:11.804 +10:00 [INF] S3StorageService initialized with bucket: yendor, UseDirectS3Urls: true, UseCDN: false
2025-07-10 01:12:11.804 +10:00 [INF] Using in-memory database for demo testing (no persistence)
2025-07-10 01:12:11.806 +10:00 [INF] Listing files in S3 with prefix: kittens/
2025-07-10 01:12:12.107 +10:00 [ERR] Error retrieving cat gallery images for category kittens
Amazon.S3.AmazonS3Exception: Malformed Access Key Id
 ---> Amazon.Runtime.Internal.HttpErrorResponseException: Exception of type 'Amazon.Runtime.Internal.HttpErrorResponseException' was thrown.
   at Amazon.Runtime.HttpWebRequestMessage.ProcessHttpResponseMessage(HttpResponseMessage responseMessage)
   at Amazon.Runtime.HttpWebRequestMessage.GetResponseAsync(CancellationToken cancellationToken)
   at Amazon.Runtime.Internal.HttpHandler`1.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RedirectHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.Unmarshaller.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.AmazonS3ResponseHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.ErrorHandler.InvokeAsync[T](IExecutionContext executionContext)
   --- End of inner exception stack trace ---
   at Amazon.Runtime.Internal.HttpErrorResponseExceptionHandler.HandleExceptionStream(IRequestContext requestContext, IWebResponseData httpErrorResponse, HttpErrorResponseException exception, Stream responseStream)
   at Amazon.Runtime.Internal.HttpErrorResponseExceptionHandler.HandleExceptionAsync(IExecutionContext executionContext, HttpErrorResponseException exception)
   at Amazon.Runtime.Internal.ExceptionHandler`1.HandleAsync(IExecutionContext executionContext, Exception exception)
   at Amazon.Runtime.Internal.ErrorHandler.ProcessExceptionAsync(IExecutionContext executionContext, Exception exception)
   at Amazon.Runtime.Internal.ErrorHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.Signer.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.S3Express.S3ExpressPreSigner.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.EndpointDiscoveryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.EndpointDiscoveryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CredentialsRetriever.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RetryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.RetryHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.CallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.S3.Internal.AmazonS3ExceptionHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.ErrorCallbackHandler.InvokeAsync[T](IExecutionContext executionContext)
   at Amazon.Runtime.Internal.MetricsHandler.InvokeAsync[T](IExecutionContext executionContext)
   at YendorCats.API.Services.S3StorageService.ListFilesAsync(String prefix) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/S3StorageService.cs:line 245
   at YendorCats.API.Controllers.CatGalleryController.ScanS3ForImagesAsync(String category) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Controllers/CatGalleryController.cs:line 94
   at YendorCats.API.Controllers.CatGalleryController.GetCategoryImages(String category, String orderBy, Boolean descending) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Controllers/CatGalleryController.cs:line 46
