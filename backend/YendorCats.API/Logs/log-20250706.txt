2025-07-06 00:35:21.284 +10:00 [INF] Starting Yendor Cats API
2025-07-06 00:36:24.021 +10:00 [WRN] Failed to determine the https port for redirect.
2025-07-06 00:36:24.038 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:36:24.043 +10:00 [ERR] Failed to get JWT settings from HashiCorp Vault, using values from appsettings
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__5(JwtBearerOptions options) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 212
2025-07-06 00:36:24.534 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:36:24.536 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:36:25.003 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:36:25.004 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:36:25.500 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:36:25.501 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:36:25.999 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:36:26.000 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:36:57.349 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:36:57.352 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:37:00.547 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:37:00.549 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:37:01.045 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:37:01.048 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:37:01.545 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:37:01.546 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:37:02.043 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:37:02.045 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:38:10.849 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:38:10.850 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:38:11.352 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:38:11.354 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:38:11.852 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:38:11.853 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:38:12.351 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:38:12.352 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:38:53.426 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:38:53.428 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:46:44.971 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:46:44.973 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:46:45.474 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:46:45.476 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:46:45.972 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:46:45.973 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:46:46.473 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:46:46.475 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:47:29.996 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:47:29.997 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:47:30.498 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:47:30.500 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:47:30.997 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:47:30.998 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:47:31.499 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:47:31.500 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:50:09.932 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:50:09.932 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:50:10.432 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:50:10.432 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:50:10.932 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:50:10.932 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:50:11.431 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:50:11.432 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:50:20.926 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:50:20.927 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:50:21.425 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:50:21.427 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:50:21.834 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:50:21.835 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:50:22.330 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:50:22.331 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:50:22.831 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:50:22.833 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:50:23.330 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:50:23.332 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:51:34.682 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:51:34.684 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:51:35.181 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:51:35.182 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:51:35.684 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:51:35.685 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:51:36.180 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:51:36.181 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:51:38.289 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:51:38.291 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:51:38.779 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:51:38.780 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:51:39.385 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:51:39.387 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:51:39.880 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:51:39.881 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:51:40.380 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:51:40.381 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:51:40.882 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:51:40.883 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:52:02.905 +10:00 [INF] Starting Yendor Cats API
2025-07-06 00:52:16.202 +10:00 [WRN] Failed to determine the https port for redirect.
2025-07-06 00:52:16.215 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:52:16.219 +10:00 [ERR] Failed to get JWT settings from HashiCorp Vault, using values from appsettings
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__5(JwtBearerOptions options) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 212
2025-07-06 00:52:16.417 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:52:16.418 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:52:16.906 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:52:16.906 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:52:17.206 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:52:17.207 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:52:17.685 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:52:17.686 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:52:18.185 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:52:18.187 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:52:18.685 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:52:18.686 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:52:26.369 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:52:26.369 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:52:26.866 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:52:26.867 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:52:27.365 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:52:27.366 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:52:27.865 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:52:27.866 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:52:29.990 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:52:29.991 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:53:08.079 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:53:08.080 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:53:08.568 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:53:08.570 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:53:09.070 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:53:09.072 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:53:09.571 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:53:09.572 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:57:41.691 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:57:41.691 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:57:42.956 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:57:42.957 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:57:42.959 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:57:42.960 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 00:57:43.956 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 00:57:43.956 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 01:17:23.412 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 01:17:23.413 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 01:17:24.076 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 01:17:24.077 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 01:17:25.032 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 01:17:25.033 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 01:17:25.033 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 01:17:25.033 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 01:17:29.672 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 01:17:29.673 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 01:17:31.035 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 01:17:31.036 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 01:17:31.036 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 01:17:31.037 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 01:17:31.420 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 01:17:31.420 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 01:17:32.034 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 01:17:32.035 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 01:17:32.785 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 01:17:32.786 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 01:17:33.510 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 01:17:33.511 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 01:17:33.783 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 01:17:33.785 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 01:17:33.928 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 01:17:33.928 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 01:17:34.424 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 01:17:34.425 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 01:17:34.924 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 01:17:34.925 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 01:17:35.329 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 01:17:35.329 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 01:17:35.826 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 01:17:35.826 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 01:17:36.326 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 01:17:36.327 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 01:17:36.826 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 01:17:36.827 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 01:17:51.649 +10:00 [INF] Starting Yendor Cats API
2025-07-06 01:18:00.048 +10:00 [WRN] Failed to determine the https port for redirect.
2025-07-06 01:18:00.060 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 01:18:00.063 +10:00 [ERR] Failed to get JWT settings from HashiCorp Vault, using values from appsettings
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__5(JwtBearerOptions options) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 212
2025-07-06 01:18:00.154 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 01:18:00.156 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 01:18:00.632 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 01:18:00.633 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 01:18:01.130 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 01:18:01.133 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 01:18:01.434 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 01:18:01.435 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 01:18:01.933 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 01:18:01.934 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 01:18:02.433 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 01:18:02.434 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
2025-07-06 01:18:02.933 +10:00 [ERR] Failed to retrieve secrets from HashiCorp Vault
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
2025-07-06 01:18:02.935 +10:00 [ERR] Unhandled exception: Vault address is not configured.
System.InvalidOperationException: Vault address is not configured.
   at SecretsManagerService.GetVaultClient() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 41
   at SecretsManagerService.GetAppSecretsAsync() in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Services/ISecretsManagerService.cs:line 83
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(IServiceProvider sp) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Program.cs:line 63
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method9(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at YendorCats.API.Middleware.ErrorHandlingMiddleware.InvokeAsync(HttpContext context) in /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/backend/YendorCats.API/Middleware/ErrorHandlingMiddleware.cs:line 43
