# Stage 1: Build the application
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Copy the solution file and restore dependencies
COPY yendorcats.sln .
COPY backend/YendorCats.API/YendorCats.API.csproj backend/YendorCats.API/
RUN dotnet restore yendorcats.sln

# Copy the rest of the application code
COPY . .
WORKDIR /src/backend/YendorCats.API
RUN dotnet publish -c Release -o /app/publish

# Stage 2: Create the runtime image
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS final
WORKDIR /app
COPY --from=build /app/publish .
ENTRYPOINT ["dotnet", "YendorCats.API.dll"]
