/**
 * Yendor Cats - Gallery JavaScript
 *
 * This file contains the functionality for the image gallery, including:
 * - Loading gallery images from API
 * - Filtering gallery images by category
 * - Handling gallery image modal for larger views
 * - Lazy loading of images for performance
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

document.addEventListener('DOMContentLoaded', function() {
    // Gallery elements
    const galleryContainer = document.getElementById('gallery-container');
    const galleryLoader = document.getElementById('gallery-loader');
    const galleryFilter = document.querySelector('.gallery-filter');
    const galleryModal = document.getElementById('gallery-modal');
    const modalImage = document.getElementById('modal-image');
    const modalCaption = document.getElementById('modal-caption');
    const modalClose = document.querySelector('.modal-close');
    const modalPrev = document.getElementById('modal-prev');
    const modalNext = document.getElementById('modal-next');

    // Check if gallery elements exist on the page
    if (!galleryContainer) return;

    // API endpoint
    const apiBaseUrl = '/api/PhotoUpload';

    // Global variables for gallery
    let allPhotos = [];
    let currentItems = [];
    let currentIndex = 0;

    /**
     * Initialize the gallery by fetching data from the API
     */
    async function initGallery() {
        try {
            if (galleryLoader) galleryLoader.style.display = 'block';

            const response = await fetch(`${apiBaseUrl}/profiles`);
            if (!response.ok) {
                throw new Error(`API request failed with status ${response.status}`);
            }

            const result = await response.json();
            if (result.success && result.profiles) {
                // Fetch photos for each profile
                const photoPromises = result.profiles.map(profile => 
                    fetch(`${apiBaseUrl}/cat/${profile.name}`).then(res => res.json())
                );
                
                const photoResults = await Promise.all(photoPromises);
                
                allPhotos = photoResults
                    .filter(res => res.success && res.photos)
                    .flatMap(res => res.photos);

                if (galleryLoader) galleryLoader.style.display = 'none';
                
                addFilterButtons(result.profiles);
                renderGalleryItems(allPhotos);
                initModal();
            } else {
                throw new Error('Failed to load profiles');
            }
        } catch (error) {
            console.error('Error initializing gallery:', error);
            if (galleryLoader) galleryLoader.innerHTML = 'Failed to load gallery.';
        }
    }

    /**
     * Create filter buttons based on unique categories from profiles
     */
    function addFilterButtons(profiles) {
        if (!galleryFilter) return;

        // Get unique categories (breeds)
        const categories = [...new Set(profiles.map(p => p.breed).filter(Boolean))];

        // Add 'All' button
        const allButton = document.createElement('button');
        allButton.classList.add('filter-btn', 'active');
        allButton.setAttribute('data-filter', 'all');
        allButton.textContent = 'All';
        allButton.addEventListener('click', () => filterGallery('all'));
        galleryFilter.appendChild(allButton);

        // Add filter buttons for each category
        categories.forEach(category => {
            const button = document.createElement('button');
            button.classList.add('filter-btn');
            button.setAttribute('data-filter', category);
            button.textContent = formatCategoryName(category);
            button.addEventListener('click', () => filterGallery(category));
            galleryFilter.appendChild(button);
        });
    }

    /**
     * Format category name for display (e.g., "maine-coon" -> "Maine Coon")
     */
    function formatCategoryName(category) {
        return category
            .split('-')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ');
    }

    /**
     * Filter gallery items by category (breed)
     */
    function filterGallery(category) {
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.classList.toggle('active', btn.getAttribute('data-filter') === category);
        });

        if (category === 'all') {
            currentItems = allPhotos;
        } else {
            currentItems = allPhotos.filter(photo => photo.breed && photo.breed.toLowerCase() === category.toLowerCase());
        }
        renderGalleryItems(currentItems);
    }

    /**
     * Render gallery items to the container
     */
    function renderGalleryItems(items) {
        // Clear the container
        galleryContainer.innerHTML = '';

        if (items.length === 0) {
            galleryContainer.innerHTML = '<p>No photos found for this category.</p>';
            return;
        }

        // Add items to the container
        items.forEach((item, index) => {
            const galleryItem = document.createElement('div');
            galleryItem.classList.add('gallery-item');
            galleryItem.setAttribute('data-id', item.photoId || index);

            const ageText = item.age ? `${item.age} years old` : '';

            galleryItem.innerHTML = `
                <img src="${item.fileUrl}" alt="${item.catName}" loading="lazy">
                <div class="gallery-item-overlay">
                    <h4>${item.catName}</h4>
                    <p>${ageText}</p>
                    <p class="description">${item.notes || ''}</p>
                </div>
            `;

            // Add click event to open modal
            galleryItem.addEventListener('click', function() {
                openModal(index, items);
            });

            galleryContainer.appendChild(galleryItem);
        });
    }

    /**
     * Initialize modal events
     */
    function initModal() {
        if (!galleryModal) return;

        // Close modal when clicking the close button
        if (modalClose) {
            modalClose.addEventListener('click', closeModal);
        }

        // Close modal when clicking outside the image
        galleryModal.addEventListener('click', function(e) {
            if (e.target === galleryModal) {
                closeModal();
            }
        });

        // Handle keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (!galleryModal.style.display || galleryModal.style.display === 'none') return;

            if (e.key === 'Escape') {
                closeModal();
            } else if (e.key === 'ArrowLeft') {
                showPrevItem();
            } else if (e.key === 'ArrowRight') {
                showNextItem();
            }
        });

        // Previous and next buttons
        if (modalPrev) {
            modalPrev.addEventListener('click', showPrevItem);
        }

        if (modalNext) {
            modalNext.addEventListener('click', showNextItem);
        }
    }

    /**
     * Open the modal with the selected image
     */
    function openModal(index, items) {
        currentIndex = index;
        currentItems = items;

        const item = items[index];

        if (modalImage) {
            modalImage.src = item.fileUrl;
            modalImage.alt = item.catName;
        }

        if (modalCaption) {
            let captionHTML = `<h4>${item.catName}</h4>`;
            if (item.notes) {
                captionHTML += `<p class="description">${item.notes}</p>`;
            }

            captionHTML += '<div class="metadata-section">';
            if (item.age) captionHTML += `<p><strong>Age:</strong> ${item.age} years</p>`;
            if (item.gender) captionHTML += `<p><strong>Gender:</strong> ${item.gender === 'M' ? 'Male' : 'Female'}</p>`;
            if (item.breed) captionHTML += `<p><strong>Breed:</strong> ${item.breed}</p>`;
            if (item.hairColor) captionHTML += `<p><strong>Hair Color:</strong> ${item.hairColor}</p>`;
            if (item.eyeColor) captionHTML += `<p><strong>Eye Color:</strong> ${item.eyeColor}</p>`;
            if (item.markings) captionHTML += `<p><strong>Markings:</strong> ${item.markings}</p>`;
            if (item.bloodline) captionHTML += `<p><strong>Bloodline:</strong> ${item.bloodline}</p>`;
            if (item.tags) captionHTML += `<p><strong>Tags:</strong> ${item.tags}</p>`;
            if (item.dateTaken) captionHTML += `<p><strong>Date Taken:</strong> ${new Date(item.dateTaken).toLocaleDateString()}</p>`;
            captionHTML += '</div>';

            modalCaption.innerHTML = captionHTML;
        }

        galleryModal.style.display = 'block';
        document.body.style.overflow = 'hidden';
    }

    /**
     * Close the modal
     */
    function closeModal() {
        galleryModal.style.display = 'none';
        document.body.style.overflow = '';
    }

    /**
     * Show the previous item in the modal
     */
    function showPrevItem() {
        currentIndex = (currentIndex - 1 + currentItems.length) % currentItems.length;
        openModal(currentIndex, currentItems);
    }

    /**
     * Show the next item in the modal
     */
    function showNextItem() {
        currentIndex = (currentIndex + 1) % currentItems.length;
        openModal(currentIndex, currentItems);
    }

    // Initialize the gallery
    initGallery();
});
